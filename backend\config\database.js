const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const logger = require('./logger');

class Database {
  constructor() {
    this.db = null;
    this.dbPath = process.env.DB_PATH || './database/salla_ai.db';
  }

  async initialize() {
    try {
      // إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
      const dbDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      // الاتصال بقاعدة البيانات
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          logger.error('خطأ في الاتصال بقاعدة البيانات:', err);
          throw err;
        }
        logger.info('تم الاتصال بقاعدة البيانات بنجاح');
      });

      // تفعيل المفاتيح الخارجية
      await this.run('PRAGMA foreign_keys = ON');

      // إنشاء الجداول
      await this.createTables();
      
      logger.info('تم تهيئة قاعدة البيانات بنجاح');
    } catch (error) {
      logger.error('فشل في تهيئة قاعدة البيانات:', error);
      throw error;
    }
  }

  async createTables() {
    const tables = [
      // جدول المتاجر
      `CREATE TABLE IF NOT EXISTS stores (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        store_id TEXT UNIQUE NOT NULL,
        store_name TEXT NOT NULL,
        store_url TEXT NOT NULL,
        access_token TEXT,
        refresh_token TEXT,
        token_expires_at DATETIME,
        webhook_secret TEXT,
        settings TEXT DEFAULT '{}',
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // جدول المحادثات
      `CREATE TABLE IF NOT EXISTS conversations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        store_id TEXT NOT NULL,
        customer_id TEXT,
        session_id TEXT NOT NULL,
        customer_name TEXT,
        customer_email TEXT,
        customer_phone TEXT,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (store_id) REFERENCES stores(store_id)
      )`,

      // جدول الرسائل
      `CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        conversation_id INTEGER NOT NULL,
        sender_type TEXT NOT NULL CHECK (sender_type IN ('customer', 'bot', 'admin')),
        message_text TEXT NOT NULL,
        message_type TEXT DEFAULT 'text',
        metadata TEXT DEFAULT '{}',
        is_read BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (conversation_id) REFERENCES conversations(id)
      )`,

      // جدول المنتجات المخزنة مؤقتاً
      `CREATE TABLE IF NOT EXISTS products_cache (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        store_id TEXT NOT NULL,
        product_id TEXT NOT NULL,
        product_data TEXT NOT NULL,
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (store_id) REFERENCES stores(store_id),
        UNIQUE(store_id, product_id)
      )`,

      // جدول إعدادات الروبوت
      `CREATE TABLE IF NOT EXISTS bot_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        store_id TEXT NOT NULL,
        setting_key TEXT NOT NULL,
        setting_value TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (store_id) REFERENCES stores(store_id),
        UNIQUE(store_id, setting_key)
      )`,

      // جدول التحليلات
      `CREATE TABLE IF NOT EXISTS analytics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        store_id TEXT NOT NULL,
        event_type TEXT NOT NULL,
        event_data TEXT DEFAULT '{}',
        user_session TEXT,
        ip_address TEXT,
        user_agent TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (store_id) REFERENCES stores(store_id)
      )`,

      // جدول الويب هوك
      `CREATE TABLE IF NOT EXISTS webhook_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        store_id TEXT NOT NULL,
        event_type TEXT NOT NULL,
        payload TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        response TEXT,
        attempts INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        processed_at DATETIME,
        FOREIGN KEY (store_id) REFERENCES stores(store_id)
      )`,

      // جدول المستخدمين الإداريين
      `CREATE TABLE IF NOT EXISTS admin_users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        full_name TEXT,
        role TEXT DEFAULT 'admin',
        is_active BOOLEAN DEFAULT 1,
        last_login DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    for (const table of tables) {
      await this.run(table);
    }

    // إنشاء الفهارس
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_conversations_store_id ON conversations(store_id)',
      'CREATE INDEX IF NOT EXISTS idx_conversations_session_id ON conversations(session_id)',
      'CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id)',
      'CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_products_cache_store_id ON products_cache(store_id)',
      'CREATE INDEX IF NOT EXISTS idx_analytics_store_id ON analytics(store_id)',
      'CREATE INDEX IF NOT EXISTS idx_analytics_event_type ON analytics(event_type)',
      'CREATE INDEX IF NOT EXISTS idx_webhook_logs_store_id ON webhook_logs(store_id)'
    ];

    for (const index of indexes) {
      await this.run(index);
    }
  }

  // دالة تنفيذ استعلام
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          logger.error('خطأ في تنفيذ الاستعلام:', err);
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  // دالة جلب سجل واحد
  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          logger.error('خطأ في جلب البيانات:', err);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // دالة جلب عدة سجلات
  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          logger.error('خطأ في جلب البيانات:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // إغلاق الاتصال
  close() {
    return new Promise((resolve, reject) => {
      this.db.close((err) => {
        if (err) {
          logger.error('خطأ في إغلاق قاعدة البيانات:', err);
          reject(err);
        } else {
          logger.info('تم إغلاق قاعدة البيانات بنجاح');
          resolve();
        }
      });
    });
  }
}

// إنشاء مثيل واحد من قاعدة البيانات
const database = new Database();

module.exports = database;
