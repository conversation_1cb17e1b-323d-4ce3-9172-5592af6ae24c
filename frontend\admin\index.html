<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة إدارة الذكاء الاصطناعي - سلة</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <h1>مساعد الذكاء الاصطناعي</h1>
                </div>
                <p>لوحة إدارة تكامل سلة</p>
            </div>

            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required>
                    </div>
                </div>

                <button type="submit" class="btn-primary" id="loginBtn">
                    <span>تسجيل الدخول</span>
                    <i class="fas fa-spinner fa-spin" style="display: none;"></i>
                </button>

                <div id="loginError" class="error-message" style="display: none;"></div>
            </form>

            <div class="login-footer">
                <p>لا تملك حساب؟ <a href="#" id="showRegister">إنشاء حساب جديد</a></p>
            </div>
        </div>
    </div>

    <!-- شاشة التسجيل -->
    <div id="registerScreen" class="login-screen" style="display: none;">
        <div class="login-container">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <h1>إنشاء حساب جديد</h1>
                </div>
                <p>انضم لمنصة الذكاء الاصطناعي</p>
            </div>

            <form id="registerForm" class="login-form">
                <div class="form-group">
                    <label for="regFullName">الاسم الكامل</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="regFullName" name="fullName" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="regUsername">اسم المستخدم</label>
                    <div class="input-group">
                        <i class="fas fa-at"></i>
                        <input type="text" id="regUsername" name="username" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="regEmail">البريد الإلكتروني</label>
                    <div class="input-group">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="regEmail" name="email" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="regPassword">كلمة المرور</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="regPassword" name="password" required>
                    </div>
                </div>

                <button type="submit" class="btn-primary" id="registerBtn">
                    <span>إنشاء الحساب</span>
                    <i class="fas fa-spinner fa-spin" style="display: none;"></i>
                </button>

                <div id="registerError" class="error-message" style="display: none;"></div>
                <div id="registerSuccess" class="success-message" style="display: none;"></div>
            </form>

            <div class="login-footer">
                <p>لديك حساب بالفعل؟ <a href="#" id="showLogin">تسجيل الدخول</a></p>
            </div>
        </div>
    </div>

    <!-- التطبيق الرئيسي -->
    <div id="mainApp" class="main-app" style="display: none;">
        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <span>مساعد الذكاء الاصطناعي</span>
                </div>
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active" data-page="dashboard">
                        <i class="fas fa-chart-line"></i>
                        <span>لوحة المعلومات</span>
                    </li>
                    <li class="nav-item" data-page="stores">
                        <i class="fas fa-store"></i>
                        <span>المتاجر</span>
                    </li>
                    <li class="nav-item" data-page="conversations">
                        <i class="fas fa-comments"></i>
                        <span>المحادثات</span>
                    </li>
                    <li class="nav-item" data-page="analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span>التحليلات</span>
                    </li>
                    <li class="nav-item" data-page="settings">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name" id="userName">المستخدم</span>
                        <span class="user-role">مدير</span>
                    </div>
                </div>
                <button id="logoutBtn" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- رأس الصفحة -->
            <header class="page-header">
                <div class="header-left">
                    <h1 id="pageTitle">لوحة المعلومات</h1>
                    <p id="pageDescription">نظرة عامة على أداء النظام</p>
                </div>
                <div class="header-right">
                    <button class="btn-secondary" id="refreshBtn">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                    <button class="btn-primary" id="addStoreBtn">
                        <i class="fas fa-plus"></i>
                        ربط متجر جديد
                    </button>
                </div>
            </header>

            <!-- محتوى الصفحات -->
            <div class="page-content">
                <!-- صفحة لوحة المعلومات -->
                <div id="dashboardPage" class="page active">
                    <!-- بطاقات الإحصائيات -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-store"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="activeStoresCount">0</h3>
                                <p>المتاجر النشطة</p>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="todayConversationsCount">0</h3>
                                <p>محادثات اليوم</p>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="todayMessagesCount">0</h3>
                                <p>رسائل اليوم</p>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="cachedProductsCount">0</h3>
                                <p>المنتجات المحفوظة</p>
                            </div>
                        </div>
                    </div>

                    <!-- الرسوم البيانية -->
                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>المحادثات الأسبوعية</h3>
                                <div class="chart-controls">
                                    <select id="chartPeriod">
                                        <option value="7d">آخر 7 أيام</option>
                                        <option value="30d">آخر 30 يوم</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-content">
                                <canvas id="conversationsChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>أحدث المحادثات</h3>
                            </div>
                            <div class="chart-content">
                                <div id="recentConversations" class="conversations-list">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة المتاجر -->
                <div id="storesPage" class="page">
                    <div class="page-section">
                        <div class="section-header">
                            <h2>المتاجر المربوطة</h2>
                            <button class="btn-primary" id="connectStoreBtn">
                                <i class="fas fa-link"></i>
                                ربط متجر جديد
                            </button>
                        </div>
                        <div id="storesList" class="stores-grid">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- صفحة المحادثات -->
                <div id="conversationsPage" class="page">
                    <div class="page-section">
                        <div class="section-header">
                            <h2>إدارة المحادثات</h2>
                            <div class="filters">
                                <select id="storeFilter">
                                    <option value="">جميع المتاجر</option>
                                </select>
                                <select id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشطة</option>
                                    <option value="ended">منتهية</option>
                                </select>
                            </div>
                        </div>
                        <div id="conversationsList" class="conversations-table">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- صفحة التحليلات -->
                <div id="analyticsPage" class="page">
                    <div class="analytics-content">
                        <h2>تحليلات مفصلة</h2>
                        <!-- سيتم إضافة المحتوى لاحقاً -->
                    </div>
                </div>

                <!-- صفحة الإعدادات -->
                <div id="settingsPage" class="page">
                    <div class="settings-content">
                        <h2>إعدادات النظام</h2>
                        <!-- سيتم إضافة المحتوى لاحقاً -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- نوافذ منبثقة -->
    <div id="modalOverlay" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">عنوان النافذة</h3>
                <button class="modal-close" id="closeModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- محتوى النافذة -->
            </div>
        </div>
    </div>

    <!-- تحميل السكريبتات -->
    <script src="script.js"></script>
</body>
</html>
