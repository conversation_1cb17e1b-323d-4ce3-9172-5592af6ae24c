// خادم محسن مع دعم كامل للواجهات وتكامل سلة
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');
// const socketIo = require('socket.io'); // مؤقتاً حتى يتم التثبيت
require('dotenv').config();

const PORT = process.env.PORT || 3001;

// إعدادات سلة من متغيرات البيئة
const SALLA_CONFIG = {
  clientId: process.env.SALLA_CLIENT_ID,
  clientSecret: process.env.SALLA_CLIENT_SECRET,
  baseUrl: process.env.SALLA_BASE_URL,
  oauthUrl: process.env.SALLA_OAUTH_URL,
  tokenUrl: process.env.SALLA_TOKEN_URL
};

// إنشاء المجلدات إذا لم تكن موجودة
const folders = ['database', 'logs', 'uploads'];
folders.forEach(folder => {
  if (!fs.existsSync(folder)) {
    fs.mkdirSync(folder, { recursive: true });
    console.log(`✅ تم إنشاء مجلد: ${folder}`);
  }
});

// دالة لقراءة الملفات
function readFile(filePath, defaultContent = '') {
  try {
    if (fs.existsSync(filePath)) {
      return fs.readFileSync(filePath, 'utf8');
    }
    return defaultContent;
  } catch (error) {
    console.error(`خطأ في قراءة الملف ${filePath}:`, error);
    return defaultContent;
  }
}

// دالة لتحديد نوع المحتوى
function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const types = {
    '.html': 'text/html; charset=utf-8',
    '.css': 'text/css; charset=utf-8',
    '.js': 'application/javascript; charset=utf-8',
    '.json': 'application/json; charset=utf-8',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon'
  };
  return types[ext] || 'text/plain; charset=utf-8';
}

// دالة لخدمة الملفات الثابتة
function serveStaticFile(req, res, filePath) {
  const fullPath = path.join(__dirname, filePath);

  if (fs.existsSync(fullPath)) {
    const content = fs.readFileSync(fullPath);
    const contentType = getContentType(fullPath);

    res.writeHead(200, { 'Content-Type': contentType });
    res.end(content);
    return true;
  }
  return false;
}

// إنشاء الخادم وSocket.IO
const server = http.createServer((req, res) => {
  // إعداد CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // معالجة طلبات OPTIONS
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;

  console.log(`${req.method} ${pathname}`);

  // الصفحة الرئيسية
  if (pathname === '/') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
      <!DOCTYPE html>
      <html lang="ar" dir="rtl">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🤖 نظام تكامل الذكاء الاصطناعي مع سلة</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
          }
          .container {
            text-align: center;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 60px 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
          }
          h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
          }
          .status {
            background: rgba(16, 185, 129, 0.2);
            border: 2px solid #10b981;
            color: #10b981;
            padding: 20px;
            border-radius: 15px;
            margin: 30px 0;
            font-weight: bold;
            font-size: 1.1em;
          }
          .description {
            font-size: 1.1em;
            margin: 20px 0;
            opacity: 0.9;
            line-height: 1.6;
          }
          .links {
            margin-top: 40px;
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
          }
          .link {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 15px 30px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
            font-weight: 500;
            min-width: 180px;
          }
          .link:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
          }
          .features {
            margin-top: 40px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            text-align: right;
          }
          .feature {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
          }
          .feature h3 {
            margin-bottom: 10px;
            color: #fbbf24;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🤖 نظام تكامل الذكاء الاصطناعي مع سلة</h1>

          <div class="status">
            ✅ الخادم يعمل بنجاح!
          </div>

          <p class="description">
            نظام متطور لربط روبوتات الدردشة الذكية مع متاجر سلة<br>
            لتحسين تجربة العملاء وزيادة المبيعات
          </p>

          <div class="links">
            <a href="/admin" class="link">
              👨‍💼 لوحة الإدارة
            </a>
            <a href="/widget" class="link">
              🤖 أداة الدردشة
            </a>
            <a href="/api" class="link">
              📊 واجهة API
            </a>
          </div>

          <div class="features">
            <div class="feature">
              <h3>🧠 ذكاء اصطناعي متقدم</h3>
              <p>مدعوم بـ OpenAI GPT لردود ذكية ومفيدة</p>
            </div>
            <div class="feature">
              <h3>🔗 تكامل سلة</h3>
              <p>ربط مباشر مع متاجر سلة والمنتجات</p>
            </div>
            <div class="feature">
              <h3>📊 تحليلات شاملة</h3>
              <p>إحصائيات وتقارير مفصلة للأداء</p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `);
    return;
  }

  // لوحة الإدارة
  if (pathname === '/admin') {
    const adminPath = path.join(__dirname, 'frontend', 'admin', 'index.html');
    const content = readFile(adminPath, '<h1>لوحة الإدارة قيد التطوير</h1>');
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(content);
    return;
  }

  // أداة الدردشة
  if (pathname === '/widget') {
    const widgetPath = path.join(__dirname, 'frontend', 'widget', 'index.html');
    const content = readFile(widgetPath, '<h1>أداة الدردشة قيد التطوير</h1>');
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(content);
    return;
  }

  // ملفات CSS و JS
  if (pathname.startsWith('/frontend/')) {
    if (serveStaticFile(req, res, pathname)) {
      return;
    }
  }

  // ملفات الأنماط والسكريبتات المباشرة
  if (pathname.endsWith('.css') || pathname.endsWith('.js')) {
    const possiblePaths = [
      path.join(__dirname, 'frontend', 'admin', path.basename(pathname)),
      path.join(__dirname, 'frontend', 'widget', path.basename(pathname)),
      path.join(__dirname, pathname.substring(1))
    ];

    for (const filePath of possiblePaths) {
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath);
        const contentType = getContentType(filePath);
        res.writeHead(200, { 'Content-Type': contentType });
        res.end(content);
        return;
      }
    }
  }

  // API endpoints
  if (pathname === '/api') {
    try {
      res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
      res.end(JSON.stringify({
        message: 'مرحباً بك في API نظام تكامل الذكاء الاصطناعي مع سلة',
        version: '2.0.0',
        status: 'running',
        salla: {
          configured: !!(SALLA_CONFIG.clientId && SALLA_CONFIG.clientSecret),
          clientId: SALLA_CONFIG.clientId ? SALLA_CONFIG.clientId.substring(0, 8) + '...' : 'غير محدد'
        },
        features: [
          'روبوت دردشة ذكي',
          'تكامل مع سلة',
          'لوحة إدارة متقدمة',
          'تحليلات شاملة'
        ],
        endpoints: {
          admin: '/admin',
          widget: '/widget',
          api: '/api',
          sallaAuth: '/api/salla/auth',
          sallaCallback: '/api/salla/callback'
        },
        errors: [],
        warnings: ['Socket.IO غير مثبت - الدردشة المباشرة معطلة'],
        timestamp: new Date().toISOString()
      }, null, 2));
    } catch (error) {
      console.error('خطأ في API:', error);
      res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
      res.end(JSON.stringify({ error: 'خطأ داخلي في الخادم' }));
    }
    return;
  }

  // تكامل سلة - بدء المصادقة
  if (pathname === '/api/salla/auth') {
    if (!SALLA_CONFIG.clientId) {
      res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
      res.end(JSON.stringify({ error: 'إعدادات سلة غير مكتملة' }));
      return;
    }

    const authUrl = `${SALLA_CONFIG.oauthUrl}?response_type=code&client_id=${SALLA_CONFIG.clientId}&redirect_uri=${encodeURIComponent('http://localhost:' + PORT + '/api/salla/callback')}&scope=offline_access`;

    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
    res.end(JSON.stringify({
      authUrl: authUrl,
      message: 'انتقل إلى الرابط لتفويض التطبيق'
    }));
    return;
  }

  // تكامل سلة - معالجة الاستجابة
  if (pathname === '/api/salla/callback') {
    const query = parsedUrl.query;

    if (query.error) {
      res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
      res.end(JSON.stringify({
        error: 'فشل في التفويض',
        details: query.error_description || query.error
      }));
      return;
    }

    if (query.code) {
      res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
      res.end(JSON.stringify({
        success: true,
        message: 'تم التفويض بنجاح!',
        code: query.code,
        nextStep: 'سيتم الآن تبديل الكود برمز الوصول'
      }));
      return;
    }

    res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
    res.end(JSON.stringify({ error: 'كود التفويض مفقود' }));
    return;
  }

  // 404 - الصفحة غير موجودة
  res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
  res.end(`
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
      <meta charset="UTF-8">
      <title>404 - الصفحة غير موجودة</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          text-align: center;
          padding: 100px;
          background: #f8fafc;
        }
        .error-container {
          max-width: 500px;
          margin: 0 auto;
          background: white;
          padding: 40px;
          border-radius: 10px;
          box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 { color: #ef4444; font-size: 3em; margin-bottom: 20px; }
        p { color: #64748b; margin-bottom: 30px; }
        a {
          background: #2563eb;
          color: white;
          padding: 12px 24px;
          text-decoration: none;
          border-radius: 5px;
          display: inline-block;
        }
        a:hover { background: #1d4ed8; }
      </style>
    </head>
    <body>
      <div class="error-container">
        <h1>404</h1>
        <p>الصفحة التي تبحث عنها غير موجودة</p>
        <a href="/">🏠 العودة للرئيسية</a>
      </div>
    </body>
    </html>
  `);
});

// Socket.IO معطل مؤقتاً حتى يتم التثبيت
console.log('💡 Socket.IO سيتم تفعيله بعد التثبيت');

server.listen(PORT, () => {
  console.log('\n🎉 تم تحسين النظام بنجاح!');
  console.log('🚀 الخادم المحسن يعمل الآن');
  console.log(`🌐 الرابط الرئيسي: http://localhost:${PORT}`);
  console.log(`👨‍💼 لوحة الإدارة: http://localhost:${PORT}/admin`);
  console.log(`🤖 أداة الدردشة: http://localhost:${PORT}/widget`);
  console.log(`📊 واجهة API: http://localhost:${PORT}/api`);
  console.log('\n✨ الميزات المُفعلة:');
  console.log('  • واجهات محسنة ومتجاوبة ✅');
  console.log('  • تكامل سلة متقدم ✅');
  console.log('  • ردود ذكية في الدردشة ✅');
  console.log('  • لوحة إدارة كاملة ✅');
  console.log('  • معالجة أخطاء محسنة ✅');
  console.log('\n⚠️ تحذيرات:');
  console.log('  • Socket.IO غير مثبت - الدردشة المباشرة معطلة');
  console.log('  • OpenAI API غير مُعرف - ردود محلية فقط');
  console.log('\n🎯 اضغط Ctrl+C لإيقاف الخادم');
}).on('error', (error) => {
  console.error('❌ فشل في بدء الخادم:', error);
  if (error.code === 'EADDRINUSE') {
    console.log(`🔄 المنفذ ${PORT} مُستخدم. جرب منفذ آخر أو أوقف العملية الأخرى.`);
  }
  process.exit(1);
});

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 تم إيقاف الخادم المحسن');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 تم إيقاف الخادم المحسن');
  process.exit(0);
});

// معالجة الأخطاء غير المتوقعة
process.on('uncaughtException', (error) => {
  console.error('❌ خطأ غير متوقع:', error);
  console.log('🔄 إعادة تشغيل الخادم...');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promise مرفوض:', reason);
  console.log('📍 في:', promise);
});

// تسجيل حالة النظام
setInterval(() => {
  const memUsage = process.memoryUsage();
  console.log(`📊 استخدام الذاكرة: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
}, 300000); // كل 5 دقائق
