<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مساعد الذكاء الاصطناعي - سلة</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- زر فتح الدردشة -->
    <div id="chatToggle" class="chat-toggle">
        <i class="fas fa-comments"></i>
        <span class="notification-badge" id="notificationBadge" style="display: none;">1</span>
    </div>

    <!-- نافذة الدردشة -->
    <div id="chatWidget" class="chat-widget">
        <!-- رأس الدردشة -->
        <div class="chat-header">
            <div class="chat-header-info">
                <div class="bot-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="bot-info">
                    <h3>مساعد الذكاء الاصطناعي</h3>
                    <span class="status online">متصل الآن</span>
                </div>
            </div>
            <div class="chat-controls">
                <button id="minimizeChat" class="control-btn">
                    <i class="fas fa-minus"></i>
                </button>
                <button id="closeChat" class="control-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- منطقة الرسائل -->
        <div id="chatMessages" class="chat-messages">
            <!-- رسالة الترحيب -->
            <div class="message bot-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="message-bubble">
                        <p>مرحباً بك! 👋</p>
                        <p>أنا مساعدك الذكي، كيف يمكنني مساعدتك اليوم؟</p>
                    </div>
                    <div class="message-time">الآن</div>
                </div>
            </div>
        </div>

        <!-- منطقة الاقتراحات السريعة -->
        <div id="quickSuggestions" class="quick-suggestions">
            <div class="suggestion-item" data-text="أريد البحث عن منتج">
                <i class="fas fa-search"></i>
                البحث عن منتج
            </div>
            <div class="suggestion-item" data-text="ما هي طرق الدفع المتاحة؟">
                <i class="fas fa-credit-card"></i>
                طرق الدفع
            </div>
            <div class="suggestion-item" data-text="معلومات عن الشحن والتوصيل">
                <i class="fas fa-truck"></i>
                الشحن والتوصيل
            </div>
            <div class="suggestion-item" data-text="أريد التحدث مع خدمة العملاء">
                <i class="fas fa-headset"></i>
                خدمة العملاء
            </div>
        </div>

        <!-- منطقة كتابة الرسالة -->
        <div class="chat-input-area">
            <div class="typing-indicator" id="typingIndicator" style="display: none;">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span>المساعد يكتب...</span>
            </div>

            <div class="chat-input-container">
                <div class="input-wrapper">
                    <textarea
                        id="messageInput"
                        placeholder="اكتب رسالتك هنا..."
                        rows="1"
                        maxlength="1000"
                    ></textarea>
                    <div class="input-actions">
                        <button id="attachBtn" class="action-btn" title="إرفاق ملف">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <button id="emojiBtn" class="action-btn" title="إضافة رمز تعبيري">
                            <i class="fas fa-smile"></i>
                        </button>
                    </div>
                </div>
                <button id="sendBtn" class="send-btn" disabled>
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>

            <div class="input-footer">
                <span class="character-count">0/1000</span>
                <span class="powered-by">مدعوم بالذكاء الاصطناعي</span>
            </div>
        </div>
    </div>

    <!-- نافذة تقييم المحادثة -->
    <div id="feedbackModal" class="feedback-modal" style="display: none;">
        <div class="feedback-content">
            <h3>تقييم المحادثة</h3>
            <p>كيف كانت تجربتك مع المساعد الذكي؟</p>

            <div class="rating-stars">
                <i class="fas fa-star" data-rating="1"></i>
                <i class="fas fa-star" data-rating="2"></i>
                <i class="fas fa-star" data-rating="3"></i>
                <i class="fas fa-star" data-rating="4"></i>
                <i class="fas fa-star" data-rating="5"></i>
            </div>

            <textarea id="feedbackText" placeholder="اترك تعليقك (اختياري)..." rows="3"></textarea>

            <div class="feedback-actions">
                <button id="submitFeedback" class="btn-primary">إرسال التقييم</button>
                <button id="skipFeedback" class="btn-secondary">تخطي</button>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <input type="file" id="fileInput" style="display: none;" accept="image/*,.pdf,.doc,.docx">

    <!-- سكريبت التكوين -->
    <script>
        // إعدادات الأداة
        window.SallaChatConfig = {
            storeId: new URLSearchParams(window.location.search).get('store') || 'demo',
            apiUrl: window.location.origin + '/api',
            socketUrl: window.location.origin,
            theme: 'default',
            position: 'bottom-right',
            language: 'ar',
            autoOpen: false,
            showWelcomeMessage: true,
            enableFileUpload: true,
            enableEmojis: true,
            maxMessageLength: 1000,
            typingIndicatorDelay: 1000,
            messageSound: true
        };
    </script>

    <!-- سكريبت الأداة -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="script.js"></script>
</body>
</html>
