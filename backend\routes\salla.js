const express = require('express');
const router = express.Router();
const sallaService = require('../services/sallaService');
const database = require('../config/database');
const logger = require('../config/logger');
const { query, validationResult } = require('express-validator');

// الحصول على رابط التفويض
router.get('/auth/url', async (req, res) => {
  try {
    const redirectUri = req.query.redirect_uri || `${req.protocol}://${req.get('host')}/api/salla/auth/callback`;
    const state = req.query.state || Math.random().toString(36).substring(7);

    const authUrl = sallaService.getAuthorizationUrl(redirectUri, state);

    logger.logStart('إنشاء رابط التفويض', { redirectUri, state });

    res.json({
      success: true,
      data: {
        authUrl: authUrl,
        state: state,
        redirectUri: redirectUri
      }
    });

  } catch (error) {
    logger.logError('فشل في إنشاء رابط التفويض', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في إنشاء رابط التفويض'
    });
  }
});

// معالجة callback التفويض
router.get('/auth/callback', async (req, res) => {
  try {
    const { code, state, error } = req.query;

    if (error) {
      logger.logError('خطأ في التفويض', new Error(error));
      return res.redirect(`/admin?error=${encodeURIComponent(error)}`);
    }

    if (!code) {
      return res.status(400).json({
        success: false,
        message: 'كود التفويض مطلوب'
      });
    }

    const redirectUri = `${req.protocol}://${req.get('host')}/api/salla/auth/callback`;
    
    // تبديل الكود برمز الوصول
    const tokens = await sallaService.exchangeCodeForToken(code, redirectUri);
    
    // جلب معلومات المتجر
    const storeInfo = await sallaService.getStoreInfo(tokens.access_token);
    
    // حفظ معلومات المتجر
    await sallaService.saveStoreInfo(storeInfo.id, storeInfo, tokens);

    // تسجيل webhooks
    try {
      const webhookUrl = `${req.protocol}://${req.get('host')}/webhooks/salla`;
      const events = [
        'order.created',
        'order.updated',
        'product.created',
        'product.updated',
        'customer.created'
      ];
      
      await sallaService.registerWebhook(storeInfo.id, events, webhookUrl);
      logger.logSuccess('تم تسجيل webhooks بنجاح');
    } catch (webhookError) {
      logger.logWarning('فشل في تسجيل webhooks', { error: webhookError.message });
    }

    logger.logSuccess('تم ربط المتجر بنجاح', { storeId: storeInfo.id });

    // إعادة توجيه للوحة الإدارة
    res.redirect(`/admin?success=true&store=${storeInfo.id}`);

  } catch (error) {
    logger.logError('فشل في معالجة callback التفويض', error);
    res.redirect(`/admin?error=${encodeURIComponent('فشل في ربط المتجر')}`);
  }
});

// جلب معلومات المتجر
router.get('/store/:storeId', async (req, res) => {
  try {
    const { storeId } = req.params;

    const storeInfo = await sallaService.getStoreInfo(storeId);

    res.json({
      success: true,
      data: storeInfo
    });

  } catch (error) {
    logger.logError('فشل في جلب معلومات المتجر', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب معلومات المتجر'
    });
  }
});

// جلب المنتجات
router.get('/store/:storeId/products', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { storeId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    const products = await sallaService.getProducts(storeId, page, limit);

    // حفظ المنتجات في الكاش
    if (products.data && products.data.length > 0) {
      for (const product of products.data) {
        await database.run(
          `INSERT OR REPLACE INTO products_cache (store_id, product_id, product_data, last_updated)
           VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
          [storeId, product.id, JSON.stringify(product)]
        );
      }
    }

    res.json({
      success: true,
      data: products
    });

  } catch (error) {
    logger.logError('فشل في جلب المنتجات', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب المنتجات'
    });
  }
});

// جلب تفاصيل منتج
router.get('/store/:storeId/products/:productId', async (req, res) => {
  try {
    const { storeId, productId } = req.params;

    const product = await sallaService.getProduct(storeId, productId);

    // تحديث الكاش
    await database.run(
      `INSERT OR REPLACE INTO products_cache (store_id, product_id, product_data, last_updated)
       VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
      [storeId, productId, JSON.stringify(product)]
    );

    res.json({
      success: true,
      data: product
    });

  } catch (error) {
    logger.logError('فشل في جلب تفاصيل المنتج', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب تفاصيل المنتج'
    });
  }
});

// جلب الطلبات
router.get('/store/:storeId/orders', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { storeId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    const orders = await sallaService.getOrders(storeId, page, limit);

    res.json({
      success: true,
      data: orders
    });

  } catch (error) {
    logger.logError('فشل في جلب الطلبات', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الطلبات'
    });
  }
});

// جلب العملاء
router.get('/store/:storeId/customers', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { storeId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    const customers = await sallaService.getCustomers(storeId, page, limit);

    res.json({
      success: true,
      data: customers
    });

  } catch (error) {
    logger.logError('فشل في جلب العملاء', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب العملاء'
    });
  }
});

// جلب الفئات
router.get('/store/:storeId/categories', async (req, res) => {
  try {
    const { storeId } = req.params;

    const categories = await sallaService.getCategories(storeId);

    res.json({
      success: true,
      data: categories
    });

  } catch (error) {
    logger.logError('فشل في جلب الفئات', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الفئات'
    });
  }
});

// جلب المتاجر المربوطة
router.get('/stores', async (req, res) => {
  try {
    const stores = await database.all(
      'SELECT store_id, store_name, store_url, is_active, created_at FROM stores ORDER BY created_at DESC'
    );

    res.json({
      success: true,
      data: stores
    });

  } catch (error) {
    logger.logError('فشل في جلب المتاجر', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب المتاجر'
    });
  }
});

// تحديث حالة المتجر
router.patch('/store/:storeId/status', async (req, res) => {
  try {
    const { storeId } = req.params;
    const { isActive } = req.body;

    await database.run(
      'UPDATE stores SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE store_id = ?',
      [isActive ? 1 : 0, storeId]
    );

    logger.logSuccess('تم تحديث حالة المتجر', { storeId, isActive });

    res.json({
      success: true,
      message: 'تم تحديث حالة المتجر بنجاح'
    });

  } catch (error) {
    logger.logError('فشل في تحديث حالة المتجر', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في تحديث حالة المتجر'
    });
  }
});

module.exports = router;
