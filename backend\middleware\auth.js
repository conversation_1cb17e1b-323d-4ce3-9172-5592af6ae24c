const jwt = require('jsonwebtoken');
const database = require('../config/database');
const logger = require('../config/logger');

// middleware للتحقق من المصادقة
const authMiddleware = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'الرمز المميز مطلوب للوصول'
      });
    }

    // فك تشفير الرمز المميز
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // التحقق من وجود المستخدم في قاعدة البيانات
    const user = await database.get(
      'SELECT id, username, email, full_name, role, is_active FROM admin_users WHERE id = ?',
      [decoded.id]
    );

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'الحساب غير نشط'
      });
    }

    // إضافة معلومات المستخدم للطلب
    req.user = user;
    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'الرمز المميز غير صحيح'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'انتهت صلاحية الرمز المميز'
      });
    }

    logger.logError('خطأ في middleware المصادقة', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في التحقق من الهوية'
    });
  }
};

// middleware للتحقق من الصلاحيات
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'يجب تسجيل الدخول أولاً'
      });
    }

    const userRoles = Array.isArray(req.user.role) ? req.user.role : [req.user.role];
    const requiredRoles = Array.isArray(roles) ? roles : [roles];

    const hasPermission = requiredRoles.some(role => userRoles.includes(role));

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية للوصول لهذا المورد'
      });
    }

    next();
  };
};

// middleware اختياري للمصادقة (لا يرفض الطلب إذا لم يكن هناك رمز)
const optionalAuth = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await database.get(
      'SELECT id, username, email, full_name, role, is_active FROM admin_users WHERE id = ? AND is_active = 1',
      [decoded.id]
    );

    req.user = user || null;
    next();

  } catch (error) {
    // في حالة الخطأ، نتجاهل المصادقة ونتابع
    req.user = null;
    next();
  }
};

module.exports = {
  authMiddleware,
  requireRole,
  optionalAuth
};
