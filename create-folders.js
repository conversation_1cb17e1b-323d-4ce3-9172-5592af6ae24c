const fs = require('fs');
const path = require('path');

// قائمة المجلدات المطلوبة
const folders = [
  'database',
  'logs', 
  'uploads'
];

console.log('🗂️ إنشاء المجلدات المطلوبة...\n');

folders.forEach(folder => {
  try {
    if (!fs.existsSync(folder)) {
      fs.mkdirSync(folder, { recursive: true });
      console.log(`✅ تم إنشاء مجلد: ${folder}`);
    } else {
      console.log(`📁 المجلد موجود بالفعل: ${folder}`);
    }
  } catch (error) {
    console.error(`❌ فشل في إنشاء مجلد ${folder}:`, error.message);
  }
});

console.log('\n🎉 تم الانتهاء من إنشاء المجلدات!');
console.log('\nالخطوات التالية:');
console.log('1. تشغيل: npm run setup');
console.log('2. تشغيل: npm start');
console.log('3. فتح المتصفح على: http://localhost:3000/admin');
