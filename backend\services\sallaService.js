const axios = require('axios');
const logger = require('../config/logger');
const database = require('../config/database');

class SallaService {
  constructor() {
    this.clientId = process.env.SALLA_CLIENT_ID;
    this.clientSecret = process.env.SALLA_CLIENT_SECRET;
    this.baseUrl = process.env.SALLA_BASE_URL || 'https://api.salla.dev/admin/v2';
    this.oauthUrl = process.env.SALLA_OAUTH_URL || 'https://accounts.salla.sa/oauth2/authorize';
    this.tokenUrl = process.env.SALLA_TOKEN_URL || 'https://accounts.salla.sa/oauth2/token';
  }

  // إنشاء رابط التفويض
  getAuthorizationUrl(redirectUri, state = null) {
    const params = new URLSearchParams({
      client_id: this.clientId,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope: 'offline_access'
    });

    if (state) {
      params.append('state', state);
    }

    const authUrl = `${this.oauthUrl}?${params.toString()}`;
    logger.logStart('إنشاء رابط التفويض', { redirectUri, state });
    
    return authUrl;
  }

  // تبديل كود التفويض برمز الوصول
  async exchangeCodeForToken(code, redirectUri) {
    try {
      logger.logStart('تبديل كود التفويض برمز الوصول', { code: code.substring(0, 10) + '...' });

      const response = await axios.post(this.tokenUrl, {
        grant_type: 'authorization_code',
        client_id: this.clientId,
        client_secret: this.clientSecret,
        code: code,
        redirect_uri: redirectUri
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      const tokenData = response.data;
      logger.logSuccess('تم الحصول على رمز الوصول بنجاح');

      return {
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        expires_in: tokenData.expires_in,
        token_type: tokenData.token_type
      };

    } catch (error) {
      logger.logError('فشل في تبديل كود التفويض', error);
      throw new Error(`فشل في الحصول على رمز الوصول: ${error.response?.data?.message || error.message}`);
    }
  }

  // تجديد رمز الوصول
  async refreshAccessToken(refreshToken) {
    try {
      logger.logStart('تجديد رمز الوصول');

      const response = await axios.post(this.tokenUrl, {
        grant_type: 'refresh_token',
        client_id: this.clientId,
        client_secret: this.clientSecret,
        refresh_token: refreshToken
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      const tokenData = response.data;
      logger.logSuccess('تم تجديد رمز الوصول بنجاح');

      return {
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        expires_in: tokenData.expires_in,
        token_type: tokenData.token_type
      };

    } catch (error) {
      logger.logError('فشل في تجديد رمز الوصول', error);
      throw new Error(`فشل في تجديد رمز الوصول: ${error.response?.data?.message || error.message}`);
    }
  }

  // إجراء طلب API مع إدارة الرموز
  async makeApiRequest(storeId, endpoint, method = 'GET', data = null, headers = {}) {
    try {
      // جلب معلومات المتجر
      const store = await database.get(
        'SELECT * FROM stores WHERE store_id = ?',
        [storeId]
      );

      if (!store) {
        throw new Error('المتجر غير موجود');
      }

      // التحقق من صلاحية الرمز
      const now = new Date();
      const tokenExpiry = new Date(store.token_expires_at);

      if (now >= tokenExpiry) {
        logger.logWarning('رمز الوصول منتهي الصلاحية، جاري التجديد');
        const newTokens = await this.refreshAccessToken(store.refresh_token);
        
        // تحديث الرموز في قاعدة البيانات
        await this.updateStoreTokens(storeId, newTokens);
        store.access_token = newTokens.access_token;
      }

      // إجراء الطلب
      const config = {
        method,
        url: `${this.baseUrl}${endpoint}`,
        headers: {
          'Authorization': `Bearer ${store.access_token}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...headers
        }
      };

      if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        config.data = data;
      }

      const startTime = Date.now();
      const response = await axios(config);
      const duration = Date.now() - startTime;

      logger.logApiCall(method, endpoint, response.status, duration, { storeId });

      return response.data;

    } catch (error) {
      logger.logError(`فشل في طلب API: ${method} ${endpoint}`, error, { storeId });
      
      if (error.response?.status === 401) {
        // محاولة تجديد الرمز مرة أخرى
        try {
          const store = await database.get('SELECT * FROM stores WHERE store_id = ?', [storeId]);
          const newTokens = await this.refreshAccessToken(store.refresh_token);
          await this.updateStoreTokens(storeId, newTokens);
          
          // إعادة المحاولة مع الرمز الجديد
          return await this.makeApiRequest(storeId, endpoint, method, data, headers);
        } catch (refreshError) {
          logger.logError('فشل في تجديد الرمز أثناء إعادة المحاولة', refreshError);
          throw new Error('انتهت صلاحية الوصول للمتجر، يرجى إعادة التفويض');
        }
      }

      throw error;
    }
  }

  // تحديث رموز المتجر في قاعدة البيانات
  async updateStoreTokens(storeId, tokens) {
    const expiresAt = new Date(Date.now() + (tokens.expires_in * 1000));
    
    await database.run(
      `UPDATE stores SET 
       access_token = ?, 
       refresh_token = ?, 
       token_expires_at = ?,
       updated_at = CURRENT_TIMESTAMP
       WHERE store_id = ?`,
      [tokens.access_token, tokens.refresh_token, expiresAt.toISOString(), storeId]
    );

    logger.logSuccess('تم تحديث رموز المتجر', { storeId });
  }

  // جلب معلومات المتجر
  async getStoreInfo(storeId) {
    return await this.makeApiRequest(storeId, '/store/info');
  }

  // جلب المنتجات
  async getProducts(storeId, page = 1, limit = 50) {
    return await this.makeApiRequest(storeId, `/products?page=${page}&per_page=${limit}`);
  }

  // جلب تفاصيل منتج
  async getProduct(storeId, productId) {
    return await this.makeApiRequest(storeId, `/products/${productId}`);
  }

  // جلب الطلبات
  async getOrders(storeId, page = 1, limit = 50) {
    return await this.makeApiRequest(storeId, `/orders?page=${page}&per_page=${limit}`);
  }

  // جلب العملاء
  async getCustomers(storeId, page = 1, limit = 50) {
    return await this.makeApiRequest(storeId, `/customers?page=${page}&per_page=${limit}`);
  }

  // جلب الفئات
  async getCategories(storeId) {
    return await this.makeApiRequest(storeId, '/categories');
  }

  // تسجيل webhook
  async registerWebhook(storeId, events, url) {
    const data = {
      name: 'AI Chatbot Integration',
      url: url,
      events: events
    };

    return await this.makeApiRequest(storeId, '/webhooks', 'POST', data);
  }

  // جلب webhooks المسجلة
  async getWebhooks(storeId) {
    return await this.makeApiRequest(storeId, '/webhooks');
  }

  // حفظ معلومات المتجر في قاعدة البيانات
  async saveStoreInfo(storeId, storeData, tokens) {
    try {
      const expiresAt = new Date(Date.now() + (tokens.expires_in * 1000));
      
      await database.run(
        `INSERT OR REPLACE INTO stores 
         (store_id, store_name, store_url, access_token, refresh_token, token_expires_at, is_active, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, 1, CURRENT_TIMESTAMP)`,
        [
          storeId,
          storeData.name || 'متجر سلة',
          storeData.domain || '',
          tokens.access_token,
          tokens.refresh_token,
          expiresAt.toISOString()
        ]
      );

      logger.logSuccess('تم حفظ معلومات المتجر', { storeId });
    } catch (error) {
      logger.logError('فشل في حفظ معلومات المتجر', error, { storeId });
      throw error;
    }
  }
}

module.exports = new SallaService();
