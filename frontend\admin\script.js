class AdminPanel {
  constructor() {
    this.apiUrl = '/api';
    this.token = localStorage.getItem('admin_token');
    this.currentUser = null;
    this.currentPage = 'dashboard';
    this.socket = null;

    this.init();
  }

  init() {
    this.bindEvents();
    this.connectSocket();
    this.checkAuth();
  }

  bindEvents() {
    // تسجيل الدخول
    document.getElementById('loginForm').addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleLogin();
    });

    // التسجيل
    document.getElementById('registerForm').addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleRegister();
    });

    // التبديل بين شاشات التسجيل
    document.getElementById('showRegister').addEventListener('click', (e) => {
      e.preventDefault();
      this.showRegisterScreen();
    });

    document.getElementById('showLogin').addEventListener('click', (e) => {
      e.preventDefault();
      this.showLoginScreen();
    });

    // تسجيل الخروج
    document.getElementById('logoutBtn').addEventListener('click', () => {
      this.handleLogout();
    });

    // التنقل في الشريط الجانبي
    document.querySelectorAll('.nav-item').forEach(item => {
      item.addEventListener('click', () => {
        const page = item.getAttribute('data-page');
        this.navigateToPage(page);
      });
    });

    // أزرار الإجراءات
    document.getElementById('refreshBtn').addEventListener('click', () => {
      this.refreshCurrentPage();
    });

    document.getElementById('addStoreBtn').addEventListener('click', () => {
      this.showConnectStoreModal();
    });

    document.getElementById('connectStoreBtn').addEventListener('click', () => {
      this.showConnectStoreModal();
    });

    // أزرار تكامل سلة
    document.getElementById('connectSallaBtn').addEventListener('click', () => {
      this.connectToSalla();
    });

    document.getElementById('testSallaBtn').addEventListener('click', () => {
      this.testSallaConnection();
    });

    // إغلاق النافذة المنبثقة
    document.getElementById('closeModal').addEventListener('click', () => {
      this.closeModal();
    });

    document.getElementById('modalOverlay').addEventListener('click', (e) => {
      if (e.target === document.getElementById('modalOverlay')) {
        this.closeModal();
      }
    });
  }

  connectSocket() {
    try {
      // التحقق من وجود Socket.IO
      if (typeof io !== 'undefined') {
        this.socket = io();

        this.socket.on('connect', () => {
          console.log('🔗 لوحة الإدارة متصلة بالخادم');
        });

        // استقبال الرسائل الجديدة
        this.socket.on('new_message', (data) => {
          this.handleNewMessage(data);
        });

        // استقبال تحديثات الإحصائيات المباشرة
        this.socket.on('stats_update', (stats) => {
          this.updateLiveStats(stats);
        });

        this.socket.on('disconnect', () => {
          console.log('🔌 انقطع الاتصال مع الخادم');
        });
      } else {
        console.log('💡 Socket.IO غير متوفر - سيتم استخدام HTTP فقط');
        this.socket = null;
      }

    } catch (error) {
      console.error('فشل في الاتصال بـ Socket.IO:', error);
      this.socket = null;
    }
  }

  handleNewMessage(data) {
    // تحديث عداد الرسائل
    const badge = document.getElementById('notificationBadge');
    if (badge) {
      badge.style.display = 'block';
      badge.textContent = parseInt(badge.textContent || '0') + 1;
    }

    // إضافة إشعار
    this.showNotification(`رسالة جديدة من متجر ${data.storeId}`, 'info');

    // تحديث قائمة المحادثات إذا كانت مفتوحة
    if (this.currentPage === 'conversations') {
      this.loadConversationsData();
    }
  }

  updateLiveStats(stats) {
    // تحديث الإحصائيات المباشرة
    if (document.getElementById('activeStoresCount')) {
      document.getElementById('todayMessagesCount').textContent = stats.totalMessages || 0;
    }
  }

  async checkAuth() {
    // تخطي المصادقة للاختبار - الدخول مباشرة للوحة الإدارة
    console.log('تخطي المصادقة للاختبار');
    this.currentUser = {
      id: 1,
      username: 'admin',
      fullName: 'مدير النظام',
      email: '<EMAIL>'
    };
    this.showMainApp();
  }

  async handleLogin() {
    const form = document.getElementById('loginForm');
    const formData = new FormData(form);
    const loginBtn = document.getElementById('loginBtn');
    const errorDiv = document.getElementById('loginError');

    // إظهار حالة التحميل
    loginBtn.querySelector('span').style.display = 'none';
    loginBtn.querySelector('i').style.display = 'inline-block';
    loginBtn.disabled = true;
    errorDiv.style.display = 'none';

    try {
      const response = await fetch(`${this.apiUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: formData.get('username'),
          password: formData.get('password')
        })
      });

      const data = await response.json();

      if (data.success) {
        this.token = data.data.token;
        this.currentUser = data.data.user;
        localStorage.setItem('admin_token', this.token);
        this.showMainApp();
      } else {
        errorDiv.textContent = data.message;
        errorDiv.style.display = 'block';
      }
    } catch (error) {
      console.error('فشل في تسجيل الدخول:', error);
      errorDiv.textContent = 'حدث خطأ في الاتصال بالخادم';
      errorDiv.style.display = 'block';
    } finally {
      // إخفاء حالة التحميل
      loginBtn.querySelector('span').style.display = 'inline';
      loginBtn.querySelector('i').style.display = 'none';
      loginBtn.disabled = false;
    }
  }

  async handleRegister() {
    const form = document.getElementById('registerForm');
    const formData = new FormData(form);
    const registerBtn = document.getElementById('registerBtn');
    const errorDiv = document.getElementById('registerError');
    const successDiv = document.getElementById('registerSuccess');

    // إظهار حالة التحميل
    registerBtn.querySelector('span').style.display = 'none';
    registerBtn.querySelector('i').style.display = 'inline-block';
    registerBtn.disabled = true;
    errorDiv.style.display = 'none';
    successDiv.style.display = 'none';

    try {
      const response = await fetch(`${this.apiUrl}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fullName: formData.get('fullName'),
          username: formData.get('username'),
          email: formData.get('email'),
          password: formData.get('password')
        })
      });

      const data = await response.json();

      if (data.success) {
        successDiv.textContent = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.';
        successDiv.style.display = 'block';
        form.reset();

        // التبديل لشاشة تسجيل الدخول بعد 2 ثانية
        setTimeout(() => {
          this.showLoginScreen();
        }, 2000);
      } else {
        errorDiv.textContent = data.message;
        errorDiv.style.display = 'block';
      }
    } catch (error) {
      console.error('فشل في التسجيل:', error);
      errorDiv.textContent = 'حدث خطأ في الاتصال بالخادم';
      errorDiv.style.display = 'block';
    } finally {
      // إخفاء حالة التحميل
      registerBtn.querySelector('span').style.display = 'inline';
      registerBtn.querySelector('i').style.display = 'none';
      registerBtn.disabled = false;
    }
  }

  handleLogout() {
    localStorage.removeItem('admin_token');
    this.token = null;
    this.currentUser = null;
    this.showLoginScreen();
  }

  showLoginScreen() {
    document.getElementById('loginScreen').style.display = 'flex';
    document.getElementById('registerScreen').style.display = 'none';
    document.getElementById('mainApp').style.display = 'none';
  }

  showRegisterScreen() {
    document.getElementById('loginScreen').style.display = 'none';
    document.getElementById('registerScreen').style.display = 'flex';
    document.getElementById('mainApp').style.display = 'none';
  }

  showMainApp() {
    document.getElementById('loginScreen').style.display = 'none';
    document.getElementById('registerScreen').style.display = 'none';
    document.getElementById('mainApp').style.display = 'flex';

    // تحديث معلومات المستخدم
    document.getElementById('userName').textContent = this.currentUser.fullName || this.currentUser.username;

    // تحميل الصفحة الافتراضية
    this.navigateToPage('dashboard');
  }

  navigateToPage(page) {
    // إزالة الفئة النشطة من جميع العناصر
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.remove('active');
    });

    document.querySelectorAll('.page').forEach(pageEl => {
      pageEl.classList.remove('active');
    });

    // إضافة الفئة النشطة للعنصر المحدد
    document.querySelector(`[data-page="${page}"]`).classList.add('active');
    document.getElementById(`${page}Page`).classList.add('active');

    this.currentPage = page;
    this.updatePageHeader(page);
    this.loadPageData(page);
  }

  updatePageHeader(page) {
    const titles = {
      dashboard: 'لوحة المعلومات',
      stores: 'إدارة المتاجر',
      conversations: 'إدارة المحادثات',
      analytics: 'التحليلات',
      settings: 'الإعدادات'
    };

    const descriptions = {
      dashboard: 'نظرة عامة على أداء النظام',
      stores: 'إدارة المتاجر المربوطة بالنظام',
      conversations: 'مراقبة وإدارة محادثات العملاء',
      analytics: 'تحليلات مفصلة للأداء',
      settings: 'إعدادات النظام والروبوت'
    };

    document.getElementById('pageTitle').textContent = titles[page];
    document.getElementById('pageDescription').textContent = descriptions[page];
  }

  async loadPageData(page) {
    switch (page) {
      case 'dashboard':
        await this.loadDashboardData();
        break;
      case 'stores':
        await this.loadStoresData();
        break;
      case 'conversations':
        await this.loadConversationsData();
        break;
      case 'analytics':
        await this.loadAnalyticsData();
        break;
      case 'settings':
        await this.loadSettingsData();
        break;
    }
  }

  async loadDashboardData() {
    // بيانات تجريبية للاختبار
    const demoData = {
      stats: {
        activeStores: 3,
        todayConversations: 47,
        todayMessages: 234,
        cachedProducts: 1250
      },
      charts: {
        weeklyConversations: [
          {date: '2024-01-09', count: 15},
          {date: '2024-01-10', count: 23},
          {date: '2024-01-11', count: 31},
          {date: '2024-01-12', count: 28},
          {date: '2024-01-13', count: 42},
          {date: '2024-01-14', count: 38},
          {date: '2024-01-15', count: 47}
        ]
      },
      recentConversations: [
        {
          id: 1,
          customer_name: 'أحمد محمد',
          store_name: 'متجر الإلكترونيات',
          status: 'active',
          created_at: new Date().toISOString()
        },
        {
          id: 2,
          customer_name: 'فاطمة علي',
          store_name: 'متجر الأزياء',
          status: 'ended',
          created_at: new Date(Date.now() - 3600000).toISOString()
        },
        {
          id: 3,
          customer_name: 'محمد سالم',
          store_name: 'متجر المنزل',
          status: 'active',
          created_at: new Date(Date.now() - 7200000).toISOString()
        }
      ]
    };

    this.updateDashboardStats(demoData);
    this.updateDashboardCharts(demoData);
    this.updateRecentConversations(demoData.recentConversations);
  }

  updateDashboardStats(data) {
    document.getElementById('activeStoresCount').textContent = data.stats.activeStores;
    document.getElementById('todayConversationsCount').textContent = data.stats.todayConversations;
    document.getElementById('todayMessagesCount').textContent = data.stats.todayMessages;
    document.getElementById('cachedProductsCount').textContent = data.stats.cachedProducts;
  }

  updateDashboardCharts(data) {
    // رسم بياني للمحادثات الأسبوعية
    const ctx = document.getElementById('conversationsChart').getContext('2d');

    if (window.conversationsChart) {
      window.conversationsChart.destroy();
    }

    const labels = data.charts.weeklyConversations.map(item => {
      const date = new Date(item.date);
      return date.toLocaleDateString('ar-SA', { weekday: 'short', month: 'short', day: 'numeric' });
    });

    const chartData = data.charts.weeklyConversations.map(item => item.count);

    window.conversationsChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'المحادثات',
          data: chartData,
          borderColor: '#2563eb',
          backgroundColor: 'rgba(37, 99, 235, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              stepSize: 1
            }
          }
        }
      }
    });
  }

  updateRecentConversations(conversations) {
    const container = document.getElementById('recentConversations');
    container.innerHTML = '';

    if (conversations.length === 0) {
      container.innerHTML = '<p style="text-align: center; color: #64748b; padding: 20px;">لا توجد محادثات حديثة</p>';
      return;
    }

    conversations.forEach(conversation => {
      const conversationEl = document.createElement('div');
      conversationEl.className = 'conversation-item';
      conversationEl.innerHTML = `
        <div class="conversation-header">
          <span class="conversation-customer">${conversation.customer_name || 'عميل مجهول'}</span>
          <span class="conversation-time">${this.formatDate(conversation.created_at)}</span>
        </div>
        <div class="conversation-store">${conversation.store_name}</div>
        <span class="conversation-status ${conversation.status}">${conversation.status === 'active' ? 'نشطة' : 'منتهية'}</span>
      `;

      conversationEl.addEventListener('click', () => {
        this.showConversationDetails(conversation.id);
      });

      container.appendChild(conversationEl);
    });
  }

  async loadStoresData() {
    // تحقق من حالة تكامل سلة
    await this.checkSallaStatus();

    // بيانات تجريبية للمتاجر
    const demoStores = [
      {
        store_id: 'store_123',
        store_name: 'متجر الإلكترونيات الذكية',
        store_url: 'https://electronics-store.salla.sa',
        is_active: true,
        created_at: '2024-01-10T10:00:00Z'
      },
      {
        store_id: 'store_456',
        store_name: 'متجر الأزياء العصرية',
        store_url: 'https://fashion-store.salla.sa',
        is_active: true,
        created_at: '2024-01-12T14:30:00Z'
      },
      {
        store_id: 'store_789',
        store_name: 'متجر المنزل والديكور',
        store_url: 'https://home-store.salla.sa',
        is_active: false,
        created_at: '2024-01-15T09:15:00Z'
      }
    ];

    this.updateStoresList(demoStores);
  }

  updateStoresList(stores) {
    const container = document.getElementById('storesList');
    container.innerHTML = '';

    if (stores.length === 0) {
      container.innerHTML = '<p style="text-align: center; color: #64748b; padding: 40px;">لا توجد متاجر مربوطة بعد</p>';
      return;
    }

    stores.forEach(store => {
      const storeEl = document.createElement('div');
      storeEl.className = 'store-card';
      storeEl.innerHTML = `
        <div class="store-header">
          <h3 class="store-name">${store.store_name}</h3>
          <span class="store-status ${store.is_active ? 'active' : 'inactive'}">
            ${store.is_active ? 'نشط' : 'غير نشط'}
          </span>
        </div>
        <div class="store-url">${store.store_url}</div>
        <div class="store-actions">
          <button class="btn-secondary btn-sm" onclick="adminPanel.viewStoreDetails('${store.store_id}')">
            <i class="fas fa-eye"></i> عرض
          </button>
          <button class="btn-primary btn-sm" onclick="adminPanel.manageStoreSettings('${store.store_id}')">
            <i class="fas fa-cog"></i> إعدادات
          </button>
        </div>
      `;
      container.appendChild(storeEl);
    });
  }

  showConnectStoreModal() {
    const modal = document.getElementById('modalOverlay');
    const title = document.getElementById('modalTitle');
    const body = document.getElementById('modalBody');

    title.textContent = 'ربط متجر جديد';
    body.innerHTML = `
      <div style="text-align: center; padding: 20px;">
        <i class="fas fa-link" style="font-size: 48px; color: #2563eb; margin-bottom: 16px;"></i>
        <h3 style="margin-bottom: 16px;">ربط متجر سلة</h3>
        <p style="color: #64748b; margin-bottom: 24px;">
          سيتم توجيهك لصفحة سلة لتفويض الوصول لمتجرك
        </p>
        <button class="btn-primary" onclick="adminPanel.initiateStoreConnection()">
          <i class="fas fa-external-link-alt"></i>
          الانتقال لسلة
        </button>
      </div>
    `;

    modal.style.display = 'flex';
  }

  async initiateStoreConnection() {
    try {
      const response = await fetch(`${this.apiUrl}/salla/auth/url`, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        window.open(data.data.authUrl, '_blank');
        this.closeModal();
      }
    } catch (error) {
      console.error('فشل في إنشاء رابط التفويض:', error);
    }
  }

  closeModal() {
    document.getElementById('modalOverlay').style.display = 'none';
  }

  refreshCurrentPage() {
    this.loadPageData(this.currentPage);
  }

  formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // دوال إضافية سيتم تطويرها لاحقاً
  async loadConversationsData() {
    console.log('تحميل بيانات المحادثات...');
  }

  async loadAnalyticsData() {
    console.log('تحميل بيانات التحليلات...');
  }

  async loadSettingsData() {
    console.log('تحميل بيانات الإعدادات...');
  }

  viewStoreDetails(storeId) {
    console.log('عرض تفاصيل المتجر:', storeId);
  }

  manageStoreSettings(storeId) {
    console.log('إدارة إعدادات المتجر:', storeId);
  }

  showConversationDetails(conversationId) {
    console.log('عرض تفاصيل المحادثة:', conversationId);
  }

  // وظائف تكامل سلة
  async checkSallaStatus() {
    try {
      const response = await fetch('/api');
      const data = await response.json();

      const statusIndicator = document.getElementById('sallaStatusIndicator');
      const statusText = document.getElementById('sallaStatusText');
      const connectBtn = document.getElementById('connectSallaBtn');
      const testBtn = document.getElementById('testSallaBtn');

      if (data.salla && data.salla.configured) {
        statusIndicator.className = 'status-indicator connected';
        statusText.textContent = `متصل (${data.salla.clientId})`;
        connectBtn.style.display = 'none';
        testBtn.style.display = 'inline-flex';
      } else {
        statusIndicator.className = 'status-indicator error';
        statusText.textContent = 'غير متصل';
        connectBtn.style.display = 'inline-flex';
        testBtn.style.display = 'none';
      }
    } catch (error) {
      console.error('فشل في التحقق من حالة سلة:', error);
      const statusIndicator = document.getElementById('sallaStatusIndicator');
      const statusText = document.getElementById('sallaStatusText');

      statusIndicator.className = 'status-indicator error';
      statusText.textContent = 'خطأ في الاتصال';
    }
  }

  async connectToSalla() {
    try {
      const response = await fetch('/api/salla/auth');
      const data = await response.json();

      if (data.authUrl) {
        // فتح نافذة جديدة للتفويض
        const authWindow = window.open(
          data.authUrl,
          'salla_auth',
          'width=600,height=700,scrollbars=yes,resizable=yes'
        );

        // مراقبة إغلاق النافذة
        const checkClosed = setInterval(() => {
          if (authWindow.closed) {
            clearInterval(checkClosed);
            // تحديث حالة الاتصال
            setTimeout(() => {
              this.checkSallaStatus();
            }, 1000);
          }
        }, 1000);

        this.showNotification('تم فتح نافذة التفويض. يرجى إكمال عملية التفويض.', 'info');
      } else {
        this.showNotification('فشل في الحصول على رابط التفويض', 'error');
      }
    } catch (error) {
      console.error('فشل في بدء عملية الربط:', error);
      this.showNotification('حدث خطأ أثناء محاولة الربط مع سلة', 'error');
    }
  }

  async testSallaConnection() {
    const testBtn = document.getElementById('testSallaBtn');
    const originalText = testBtn.innerHTML;

    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';
    testBtn.disabled = true;

    try {
      // محاولة الوصول لـ API سلة
      const response = await fetch('/api');
      const data = await response.json();

      if (data.salla && data.salla.configured) {
        this.showNotification('الاتصال مع سلة يعمل بشكل صحيح!', 'success');
      } else {
        this.showNotification('لم يتم تكوين سلة بعد', 'warning');
      }
    } catch (error) {
      console.error('فشل في اختبار الاتصال:', error);
      this.showNotification('فشل في اختبار الاتصال مع سلة', 'error');
    } finally {
      testBtn.innerHTML = originalText;
      testBtn.disabled = false;
    }
  }

  showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
      </div>
      <button class="notification-close" onclick="this.parentElement.remove()">
        <i class="fas fa-times"></i>
      </button>
    `;

    // إضافة الأنماط إذا لم تكن موجودة
    if (!document.getElementById('notificationStyles')) {
      const styles = document.createElement('style');
      styles.id = 'notificationStyles';
      styles.textContent = `
        .notification {
          position: fixed;
          top: 20px;
          left: 20px;
          background: white;
          border-radius: 8px;
          padding: 16px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          z-index: 1000;
          display: flex;
          align-items: center;
          justify-content: space-between;
          min-width: 300px;
          border-left: 4px solid;
          animation: slideIn 0.3s ease;
        }
        .notification-success { border-left-color: #10b981; }
        .notification-error { border-left-color: #ef4444; }
        .notification-warning { border-left-color: #f59e0b; }
        .notification-info { border-left-color: #2563eb; }
        .notification-content { display: flex; align-items: center; gap: 8px; }
        .notification-close { background: none; border: none; cursor: pointer; color: #64748b; }
        @keyframes slideIn { from { transform: translateX(-100%); } to { transform: translateX(0); } }
      `;
      document.head.appendChild(styles);
    }

    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);
  }
}

// تهيئة لوحة الإدارة
let adminPanel;
document.addEventListener('DOMContentLoaded', () => {
  adminPanel = new AdminPanel();
});
