# 📖 دليل التثبيت والتشغيل

## 🎯 نظرة عامة

نظام تكامل الذكاء الاصطناعي مع سلة هو حل متكامل لربط روبوتات الدردشة الذكية مع متاجر سلة لتحسين تجربة العملاء وزيادة المبيعات.

## ✨ الميزات الرئيسية

- 🤖 روبوت دردشة ذكي مدعوم بـ OpenAI
- 🔄 تكامل مباشر مع Salla API
- 📊 لوحة تحكم إدارية شاملة
- 📈 تحليلات وتقارير مفصلة
- 🔍 تحسين SEO تلقائي
- 📱 تصميم متجاوب للجوال
- 🔐 نظام أمان متقدم
- 🌐 دعم متعدد اللغات (العربية والإنجليزية)

## 📋 المتطلبات

### متطلبات النظام
- **Node.js**: الإصدار 16.0.0 أو أحدث
- **npm**: الإصدار 8.0.0 أو أحدث
- **نظام التشغيل**: Windows, macOS, Linux

### الحسابات المطلوبة
- **حساب سلة للشركاء**: [salla.partners](https://salla.partners)
- **حساب OpenAI**: [platform.openai.com](https://platform.openai.com)

## 🚀 التثبيت السريع

### 1. تحميل المشروع
```bash
# استنساخ المشروع
git clone https://github.com/your-repo/salla-ai-integration.git
cd salla-ai-integration

# أو تحميل الملفات مباشرة
```

### 2. تثبيت المتطلبات
```bash
npm install
```

### 3. تشغيل معالج الإعداد
```bash
npm run setup
```

سيقوم المعالج بطرح الأسئلة التالية:

#### إعدادات الخادم
- **منفذ الخادم**: المنفذ الذي سيعمل عليه الخادم (افتراضي: 3000)
- **بيئة التشغيل**: development أو production

#### إعدادات Salla API
- **Client ID**: من لوحة تحكم شركاء سلة
- **Client Secret**: من لوحة تحكم شركاء سلة

#### إعدادات OpenAI
- **API Key**: مفتاح API من OpenAI
- **النموذج**: gpt-3.5-turbo أو gpt-4

### 4. تشغيل النظام
```bash
npm start
```

## 🔧 الإعداد اليدوي

إذا كنت تفضل الإعداد اليدوي، اتبع الخطوات التالية:

### 1. إنشاء ملف البيئة
انسخ ملف `.env.example` إلى `.env`:
```bash
cp .env.example .env
```

### 2. تحرير ملف البيئة
افتح ملف `.env` وأدخل البيانات المطلوبة:

```env
# إعدادات الخادم
PORT=3000
NODE_ENV=development

# إعدادات Salla API
SALLA_CLIENT_ID=your_client_id_here
SALLA_CLIENT_SECRET=your_client_secret_here

# إعدادات OpenAI
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# إعدادات الأمان (قم بتوليد مفاتيح عشوائية)
JWT_SECRET=your_super_secret_jwt_key_here
ENCRYPTION_KEY=your_encryption_key_here
WEBHOOK_SECRET=your_webhook_secret_here
```

### 3. إنشاء المجلدات المطلوبة
```bash
mkdir -p database logs uploads
```

## 🏪 إعداد حساب سلة للشركاء

### 1. إنشاء حساب
1. اذهب إلى [salla.partners](https://salla.partners)
2. أنشئ حساب جديد أو سجل دخول
3. أكمل عملية التحقق

### 2. إنشاء تطبيق جديد
1. من لوحة التحكم، اختر "إنشاء تطبيق جديد"
2. أدخل معلومات التطبيق:
   - **اسم التطبيق**: مساعد الذكاء الاصطناعي
   - **الوصف**: روبوت دردشة ذكي للمتاجر
   - **رابط التطبيق**: `http://localhost:3000`
   - **رابط الإعادة**: `http://localhost:3000/api/salla/auth/callback`

### 3. تكوين الصلاحيات
حدد الصلاحيات التالية:
- ✅ قراءة معلومات المتجر
- ✅ قراءة المنتجات
- ✅ قراءة الطلبات
- ✅ قراءة العملاء
- ✅ قراءة الفئات
- ✅ إدارة الويب هوك

### 4. الحصول على بيانات API
بعد إنشاء التطبيق، ستحصل على:
- **Client ID**: معرف العميل
- **Client Secret**: سر العميل

## 🧠 إعداد OpenAI

### 1. إنشاء حساب
1. اذهب إلى [platform.openai.com](https://platform.openai.com)
2. أنشئ حساب جديد أو سجل دخول

### 2. الحصول على API Key
1. اذهب إلى [API Keys](https://platform.openai.com/api-keys)
2. انقر على "Create new secret key"
3. انسخ المفتاح واحفظه بأمان

### 3. إعداد الفوترة
تأكد من إعداد طريقة دفع في حسابك لاستخدام API.

## 🔗 إعداد الويب هوك

### 1. للتطوير المحلي
استخدم أدوات مثل ngrok لعرض الخادم المحلي:
```bash
# تثبيت ngrok
npm install -g ngrok

# تشغيل ngrok
ngrok http 3000
```

### 2. للإنتاج
تأكد من أن الخادم متاح عبر الإنترنت وأن رابط الويب هوك صحيح:
```
https://yourdomain.com/webhooks/salla
```

## 🎮 تشغيل النظام

### 1. وضع التطوير
```bash
npm run dev
```

### 2. وضع الإنتاج
```bash
npm start
```

### 3. تشغيل الاختبارات
```bash
npm test
```

## 🌐 الوصول للنظام

بعد تشغيل النظام، يمكنك الوصول إلى:

- **لوحة الإدارة**: `http://localhost:3000/admin`
- **أداة الدردشة**: `http://localhost:3000/widget`
- **API الرئيسي**: `http://localhost:3000/api`

## 👤 إنشاء حساب إداري

### الطريقة الأولى: من لوحة الإدارة
1. اذهب إلى `http://localhost:3000/admin`
2. انقر على "إنشاء حساب جديد"
3. أدخل البيانات المطلوبة

### الطريقة الثانية: من API
```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "email": "<EMAIL>",
    "password": "password123",
    "fullName": "المدير العام"
  }'
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
```
Error: SQLITE_CANTOPEN: unable to open database file
```
**الحل**: تأكد من وجود مجلد `database` وصلاحيات الكتابة.

#### 2. خطأ في مفتاح OpenAI
```
Error: Invalid API key provided
```
**الحل**: تحقق من صحة مفتاح OpenAI في ملف `.env`.

#### 3. خطأ في بيانات Salla
```
Error: Invalid client credentials
```
**الحل**: تحقق من صحة Client ID و Client Secret.

#### 4. مشكلة في المنفذ
```
Error: EADDRINUSE: address already in use
```
**الحل**: غير المنفذ في ملف `.env` أو أوقف العملية التي تستخدم المنفذ.

### تفعيل وضع التشخيص
```bash
LOG_LEVEL=debug npm start
```

## 📊 مراقبة النظام

### ملفات السجل
- **السجل العام**: `logs/combined.log`
- **سجل الأخطاء**: `logs/error.log`
- **السجل اليومي**: `logs/app-YYYY-MM-DD.log`

### مراقبة الأداء
```bash
# عرض استخدام الذاكرة
node --inspect backend/server.js

# مراقبة ملفات السجل
tail -f logs/combined.log
```

## 🔄 التحديث

### تحديث النظام
```bash
# إيقاف النظام
npm stop

# تحديث الملفات
git pull origin main

# تثبيت التحديثات
npm install

# تشغيل النظام
npm start
```

### نسخ احتياطي لقاعدة البيانات
```bash
# إنشاء نسخة احتياطية
cp database/salla_ai.db database/backup_$(date +%Y%m%d_%H%M%S).db
```

## 📞 الدعم الفني

إذا واجهت أي مشاكل:

1. **تحقق من ملفات السجل** في مجلد `logs/`
2. **راجع الوثائق** في مجلد `docs/`
3. **ابحث في المشاكل المعروفة** في GitHub Issues
4. **اتصل بالدعم الفني** عبر البريد الإلكتروني

## 📝 ملاحظات مهمة

- 🔐 **الأمان**: لا تشارك مفاتيح API مع أحد
- 💾 **النسخ الاحتياطي**: قم بعمل نسخ احتياطية دورية لقاعدة البيانات
- 🔄 **التحديثات**: تابع التحديثات الأمنية والوظائف الجديدة
- 📊 **المراقبة**: راقب استخدام API لتجنب تجاوز الحدود

---

**تم التطوير بواسطة Augment Agent** 🤖
