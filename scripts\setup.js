#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const crypto = require('crypto');

class SetupWizard {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    this.config = {};
  }

  async run() {
    console.log('\n🤖 مرحباً بك في معالج إعداد نظام تكامل الذكاء الاصطناعي مع سلة\n');
    
    try {
      await this.collectConfiguration();
      await this.createEnvironmentFile();
      await this.createDirectories();
      await this.displaySummary();
      
      console.log('\n✅ تم إعداد النظام بنجاح!');
      console.log('\nالخطوات التالية:');
      console.log('1. تشغيل: npm install');
      console.log('2. تشغيل: npm start');
      console.log('3. فتح المتصفح على: http://localhost:' + (this.config.port || 3000));
      
    } catch (error) {
      console.error('\n❌ حدث خطأ أثناء الإعداد:', error.message);
    } finally {
      this.rl.close();
    }
  }

  async collectConfiguration() {
    console.log('📋 إعداد التكوين الأساسي:\n');

    // إعدادات الخادم
    this.config.port = await this.askQuestion('منفذ الخادم (افتراضي: 3000): ') || '3000';
    this.config.nodeEnv = await this.askQuestion('بيئة التشغيل (development/production) [development]: ') || 'development';

    // إعدادات قاعدة البيانات
    console.log('\n📊 إعدادات قاعدة البيانات:');
    this.config.dbPath = await this.askQuestion('مسار قاعدة البيانات [./database/salla_ai.db]: ') || './database/salla_ai.db';

    // إعدادات Salla API
    console.log('\n🏪 إعدادات Salla API:');
    console.log('يمكنك الحصول على هذه البيانات من لوحة تحكم شركاء سلة: https://salla.partners');
    
    this.config.sallaClientId = await this.askQuestion('Salla Client ID: ', true);
    this.config.sallaClientSecret = await this.askQuestion('Salla Client Secret: ', true);

    // إعدادات OpenAI
    console.log('\n🧠 إعدادات OpenAI:');
    console.log('احصل على مفتاح API من: https://platform.openai.com/api-keys');
    
    this.config.openaiApiKey = await this.askQuestion('OpenAI API Key: ', true);
    this.config.openaiModel = await this.askQuestion('نموذج OpenAI [gpt-3.5-turbo]: ') || 'gpt-3.5-turbo';

    // إعدادات الأمان
    console.log('\n🔐 إعدادات الأمان:');
    this.config.jwtSecret = this.generateSecretKey();
    this.config.encryptionKey = this.generateSecretKey();
    this.config.webhookSecret = this.generateSecretKey();

    console.log('تم إنشاء مفاتيح الأمان تلقائياً ✓');

    // إعدادات الويب هوك
    console.log('\n🔗 إعدادات الويب هوك:');
    const defaultWebhookUrl = `http://localhost:${this.config.port}/webhooks/salla`;
    this.config.webhookUrl = await this.askQuestion(`رابط الويب هوك [${defaultWebhookUrl}]: `) || defaultWebhookUrl;

    // إعدادات البريد الإلكتروني (اختيارية)
    console.log('\n📧 إعدادات البريد الإلكتروني (اختيارية):');
    const setupEmail = await this.askQuestion('هل تريد إعداد البريد الإلكتروني؟ (y/n) [n]: ') || 'n';
    
    if (setupEmail.toLowerCase() === 'y') {
      this.config.emailHost = await this.askQuestion('خادم البريد الإلكتروني [smtp.gmail.com]: ') || 'smtp.gmail.com';
      this.config.emailPort = await this.askQuestion('منفذ البريد الإلكتروني [587]: ') || '587';
      this.config.emailUser = await this.askQuestion('البريد الإلكتروني: ');
      this.config.emailPass = await this.askQuestion('كلمة مرور البريد الإلكتروني: ', true);
    }

    // إعدادات التخزين
    console.log('\n💾 إعدادات التخزين:');
    this.config.uploadPath = await this.askQuestion('مجلد التحميلات [./uploads]: ') || './uploads';
    this.config.maxFileSize = await this.askQuestion('الحد الأقصى لحجم الملف (بايت) [5242880]: ') || '5242880';

    // إعدادات التسجيل
    console.log('\n📝 إعدادات التسجيل:');
    this.config.logLevel = await this.askQuestion('مستوى التسجيل (error/warn/info/debug) [info]: ') || 'info';
    this.config.logFile = await this.askQuestion('ملف السجل [./logs/app.log]: ') || './logs/app.log';
  }

  async createEnvironmentFile() {
    console.log('\n📄 إنشاء ملف البيئة...');

    const envContent = `# إعدادات الخادم
PORT=${this.config.port}
NODE_ENV=${this.config.nodeEnv}

# إعدادات قاعدة البيانات
DB_PATH=${this.config.dbPath}

# إعدادات Salla API
SALLA_CLIENT_ID=${this.config.sallaClientId}
SALLA_CLIENT_SECRET=${this.config.sallaClientSecret}
SALLA_BASE_URL=https://api.salla.dev/admin/v2
SALLA_OAUTH_URL=https://accounts.salla.sa/oauth2/authorize
SALLA_TOKEN_URL=https://accounts.salla.sa/oauth2/token

# إعدادات OpenAI
OPENAI_API_KEY=${this.config.openaiApiKey}
OPENAI_MODEL=${this.config.openaiModel}

# إعدادات الأمان
JWT_SECRET=${this.config.jwtSecret}
ENCRYPTION_KEY=${this.config.encryptionKey}

# إعدادات الويب هوك
WEBHOOK_SECRET=${this.config.webhookSecret}
WEBHOOK_URL=${this.config.webhookUrl}

# إعدادات البريد الإلكتروني
EMAIL_HOST=${this.config.emailHost || ''}
EMAIL_PORT=${this.config.emailPort || ''}
EMAIL_USER=${this.config.emailUser || ''}
EMAIL_PASS=${this.config.emailPass || ''}

# إعدادات التخزين
UPLOAD_PATH=${this.config.uploadPath}
MAX_FILE_SIZE=${this.config.maxFileSize}

# إعدادات التسجيل
LOG_LEVEL=${this.config.logLevel}
LOG_FILE=${this.config.logFile}
`;

    fs.writeFileSync('.env', envContent);
    console.log('✓ تم إنشاء ملف .env');
  }

  async createDirectories() {
    console.log('\n📁 إنشاء المجلدات المطلوبة...');

    const directories = [
      'database',
      'logs',
      'uploads',
      path.dirname(this.config.uploadPath),
      path.dirname(this.config.logFile),
      path.dirname(this.config.dbPath)
    ];

    directories.forEach(dir => {
      if (dir && !fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`✓ تم إنشاء مجلد: ${dir}`);
      }
    });
  }

  async displaySummary() {
    console.log('\n📋 ملخص الإعداد:');
    console.log('================');
    console.log(`🌐 منفذ الخادم: ${this.config.port}`);
    console.log(`🏪 Salla Client ID: ${this.config.sallaClientId.substring(0, 8)}...`);
    console.log(`🧠 نموذج OpenAI: ${this.config.openaiModel}`);
    console.log(`📊 قاعدة البيانات: ${this.config.dbPath}`);
    console.log(`🔗 رابط الويب هوك: ${this.config.webhookUrl}`);
    console.log(`📝 مستوى التسجيل: ${this.config.logLevel}`);
    
    if (this.config.emailUser) {
      console.log(`📧 البريد الإلكتروني: ${this.config.emailUser}`);
    }
  }

  askQuestion(question, isSecret = false) {
    return new Promise((resolve) => {
      if (isSecret) {
        // إخفاء المدخل للبيانات الحساسة
        this.rl.question(question, (answer) => {
          resolve(answer);
        });
        this.rl.stdoutMuted = true;
        this.rl._writeToOutput = function _writeToOutput(stringToWrite) {
          if (this.stdoutMuted) {
            this.output.write('*');
          } else {
            this.output.write(stringToWrite);
          }
        };
      } else {
        this.rl.question(question, (answer) => {
          resolve(answer);
        });
      }
    });
  }

  generateSecretKey() {
    return crypto.randomBytes(32).toString('hex');
  }
}

// تشغيل معالج الإعداد
if (require.main === module) {
  const wizard = new SetupWizard();
  wizard.run().catch(console.error);
}

module.exports = SetupWizard;
