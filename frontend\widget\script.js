class SallaChatWidget {
  constructor(config) {
    this.config = { ...window.SallaChatConfig, ...config };
    this.socket = null;
    this.sessionId = this.generateSessionId();
    this.isOpen = false;
    this.isMinimized = false;
    this.currentRating = 0;

    this.init();
  }

  init() {
    this.bindEvents();
    this.connectSocket();
    this.loadConversationHistory();

    if (this.config.autoOpen) {
      this.openChat();
    }
  }

  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  bindEvents() {
    // زر فتح/إغلاق الدردشة
    document.getElementById('chatToggle').addEventListener('click', () => {
      this.toggleChat();
    });

    // أزرار التحكم
    document.getElementById('minimizeChat').addEventListener('click', () => {
      this.minimizeChat();
    });

    document.getElementById('closeChat').addEventListener('click', () => {
      this.closeChat();
    });

    // إرسال الرسالة
    document.getElementById('sendBtn').addEventListener('click', () => {
      this.sendMessage();
    });

    // مدخل النص
    const messageInput = document.getElementById('messageInput');
    messageInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.sendMessage();
      }
    });

    messageInput.addEventListener('input', () => {
      this.handleInputChange();
    });

    // الاقتراحات السريعة
    document.getElementById('quickSuggestions').addEventListener('click', (e) => {
      if (e.target.classList.contains('suggestion-item')) {
        const text = e.target.getAttribute('data-text');
        this.sendMessage(text);
      }
    });

    // تقييم المحادثة
    document.querySelectorAll('.rating-stars i').forEach((star, index) => {
      star.addEventListener('click', () => {
        this.setRating(index + 1);
      });
    });

    document.getElementById('submitFeedback').addEventListener('click', () => {
      this.submitFeedback();
    });

    document.getElementById('skipFeedback').addEventListener('click', () => {
      this.closeFeedbackModal();
    });

    // رفع الملفات
    document.getElementById('attachBtn').addEventListener('click', () => {
      document.getElementById('fileInput').click();
    });

    document.getElementById('fileInput').addEventListener('change', (e) => {
      this.handleFileUpload(e.target.files[0]);
    });
  }

  connectSocket() {
    // تعطيل Socket.IO مؤقتاً للاختبار
    console.log('Socket.IO معطل مؤقتاً - استخدام HTTP فقط');
    this.socket = null;
  }

  async loadConversationHistory() {
    try {
      const response = await fetch(
        `${this.config.apiUrl}/chatbot/conversation/${this.sessionId}?storeId=${this.config.storeId}`
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data.messages.length > 0) {
          // إخفاء رسالة الترحيب الافتراضية
          document.querySelector('.chat-messages').innerHTML = '';

          // عرض الرسائل السابقة
          data.data.messages.forEach(msg => {
            this.addMessage(msg.sender_type, msg.message_text, msg.metadata);
          });
        }
      }
    } catch (error) {
      console.error('فشل في تحميل تاريخ المحادثة:', error);
    }
  }

  toggleChat() {
    if (this.isOpen) {
      this.closeChat();
    } else {
      this.openChat();
    }
  }

  openChat() {
    document.getElementById('chatWidget').classList.add('open');
    document.getElementById('chatToggle').style.display = 'none';
    this.isOpen = true;
    this.scrollToBottom();
  }

  closeChat() {
    document.getElementById('chatWidget').classList.remove('open');
    document.getElementById('chatToggle').style.display = 'flex';
    this.isOpen = false;

    // عرض نافذة التقييم إذا كانت هناك رسائل
    const messages = document.querySelectorAll('.user-message');
    if (messages.length > 0) {
      this.showFeedbackModal();
    }
  }

  minimizeChat() {
    const widget = document.getElementById('chatWidget');
    if (this.isMinimized) {
      widget.classList.remove('minimized');
      this.isMinimized = false;
    } else {
      widget.classList.add('minimized');
      this.isMinimized = true;
    }
  }

  handleInputChange() {
    const input = document.getElementById('messageInput');
    const sendBtn = document.getElementById('sendBtn');
    const charCount = document.querySelector('.character-count');

    const length = input.value.length;
    charCount.textContent = `${length}/${this.config.maxMessageLength}`;

    sendBtn.disabled = length === 0 || length > this.config.maxMessageLength;

    // تعديل ارتفاع النص
    input.style.height = 'auto';
    input.style.height = Math.min(input.scrollHeight, 100) + 'px';
  }

  async sendMessage(text = null) {
    const input = document.getElementById('messageInput');
    const message = text || input.value.trim();

    if (!message) return;

    // إضافة رسالة المستخدم
    this.addMessage('user', message);

    // مسح المدخل
    if (!text) {
      input.value = '';
      this.handleInputChange();
    }

    // إخفاء الاقتراحات
    this.hideSuggestions();

    // عرض مؤشر الكتابة
    this.showTypingIndicator();

    // محاكاة رد ذكي للاختبار
    setTimeout(() => {
      this.hideTypingIndicator();

      // ردود ذكية بناءً على الرسالة
      let botResponse = this.generateSmartResponse(message);

      this.addMessage('bot', botResponse.message, botResponse);
    }, 1500); // تأخير لمحاكاة التفكير
  }

  generateSmartResponse(message) {
    const msg = message.toLowerCase();

    // ردود ذكية بناءً على المحتوى
    if (msg.includes('مرحبا') || msg.includes('السلام') || msg.includes('أهلا')) {
      return {
        message: 'مرحباً بك! 👋 أنا مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟',
        suggestions: ['البحث عن منتج', 'طرق الدفع', 'معلومات الشحن', 'خدمة العملاء'],
        products: []
      };
    }

    if (msg.includes('منتج') || msg.includes('بحث') || msg.includes('أريد')) {
      return {
        message: 'ممتاز! سأساعدك في العثور على المنتج المناسب. ما نوع المنتج الذي تبحث عنه؟ 🔍',
        suggestions: ['إلكترونيات', 'ملابس', 'منزل ومطبخ', 'رياضة وترفيه'],
        products: [
          {
            id: 'demo1',
            name: 'آيفون 15 برو',
            price: '4,999 ريال',
            image: 'https://via.placeholder.com/150x150/2563eb/ffffff?text=iPhone',
            url: '#'
          },
          {
            id: 'demo2',
            name: 'سامسونج جالاكسي',
            price: '3,499 ريال',
            image: 'https://via.placeholder.com/150x150/10b981/ffffff?text=Samsung',
            url: '#'
          }
        ]
      };
    }

    if (msg.includes('دفع') || msg.includes('شراء') || msg.includes('طريقة')) {
      return {
        message: 'نوفر عدة طرق دفع آمنة ومريحة: 💳\n\n• الدفع عند الاستلام\n• بطاقات الائتمان (فيزا، ماستركارد)\n• مدى\n• أبل باي\n• STC Pay\n• تابي (اشتر الآن وادفع لاحقاً)',
        suggestions: ['معلومات الشحن', 'سياسة الإرجاع', 'خدمة العملاء'],
        products: []
      };
    }

    if (msg.includes('شحن') || msg.includes('توصيل') || msg.includes('تسليم')) {
      return {
        message: 'معلومات الشحن والتوصيل: 🚚\n\n• التوصيل داخل الرياض: 1-2 أيام عمل\n• التوصيل لباقي المدن: 2-5 أيام عمل\n• الشحن مجاني للطلبات أكثر من 200 ريال\n• تكلفة الشحن: 15 ريال',
        suggestions: ['تتبع الطلب', 'تغيير العنوان', 'طرق الدفع'],
        products: []
      };
    }

    if (msg.includes('مساعدة') || msg.includes('دعم') || msg.includes('عملاء')) {
      return {
        message: 'يسعدني مساعدتك! 😊 يمكنك التواصل مع فريق خدمة العملاء:\n\n📞 الهاتف: 920000000\n📧 البريد: <EMAIL>\n💬 الواتساب: +966501234567\n\nأو يمكنني مساعدتك مباشرة. ما الذي تحتاج إليه؟',
        suggestions: ['مشكلة في الطلب', 'استفسار عن منتج', 'تغيير الطلب', 'إرجاع منتج'],
        products: []
      };
    }

    // رد افتراضي
    return {
      message: 'شكراً لك على رسالتك! 🙏 أفهم أنك تسأل عن "' + message + '". دعني أساعدك بأفضل طريقة ممكنة. هل يمكنك توضيح أكثر أو اختيار من الاقتراحات أدناه؟',
      suggestions: ['البحث عن منتج', 'طرق الدفع', 'معلومات الشحن', 'خدمة العملاء'],
      products: []
    };
  }

  addMessage(sender, text, metadata = {}) {
    const messagesContainer = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;

    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';
    avatarDiv.innerHTML = sender === 'bot' ? '<i class="fas fa-robot"></i>' : '<i class="fas fa-user"></i>';

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';

    const bubbleDiv = document.createElement('div');
    bubbleDiv.className = 'message-bubble';
    bubbleDiv.innerHTML = this.formatMessage(text);

    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = this.formatTime(new Date());

    contentDiv.appendChild(bubbleDiv);
    contentDiv.appendChild(timeDiv);

    messageDiv.appendChild(avatarDiv);
    messageDiv.appendChild(contentDiv);

    // إضافة المنتجات المقترحة
    if (metadata.products && metadata.products.length > 0) {
      const productsDiv = this.createProductsSection(metadata.products);
      contentDiv.appendChild(productsDiv);
    }

    // إضافة الاقتراحات
    if (metadata.suggestions && metadata.suggestions.length > 0) {
      this.updateSuggestions(metadata.suggestions);
    }

    messagesContainer.appendChild(messageDiv);
    this.scrollToBottom();

    // تشغيل صوت الإشعار
    if (sender === 'bot' && this.config.messageSound) {
      this.playNotificationSound();
    }
  }

  createProductsSection(products) {
    const productsDiv = document.createElement('div');
    productsDiv.className = 'suggested-products';

    products.forEach(product => {
      const productCard = document.createElement('div');
      productCard.className = 'product-card';
      productCard.onclick = () => window.open(product.url, '_blank');

      productCard.innerHTML = `
        <div class="product-image" style="background-image: url('${product.image}')"></div>
        <div class="product-name">${product.name}</div>
        <div class="product-price">${product.price}</div>
      `;

      productsDiv.appendChild(productCard);
    });

    return productsDiv;
  }

  updateSuggestions(suggestions) {
    const suggestionsContainer = document.getElementById('quickSuggestions');
    suggestionsContainer.innerHTML = '';

    suggestions.forEach(suggestion => {
      const suggestionDiv = document.createElement('div');
      suggestionDiv.className = 'suggestion-item';
      suggestionDiv.setAttribute('data-text', suggestion);
      suggestionDiv.innerHTML = `<i class="fas fa-comment"></i> ${suggestion}`;
      suggestionsContainer.appendChild(suggestionDiv);
    });

    this.showSuggestions();
  }

  formatMessage(text) {
    // تحويل الروابط إلى عناصر قابلة للنقر
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    text = text.replace(urlRegex, '<a href="$1" target="_blank">$1</a>');

    // تحويل النص إلى HTML مع الحفاظ على الأسطر الجديدة
    return text.replace(/\n/g, '<br>');
  }

  formatTime(date) {
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  showTypingIndicator() {
    document.getElementById('typingIndicator').style.display = 'flex';
    this.scrollToBottom();
  }

  hideTypingIndicator() {
    document.getElementById('typingIndicator').style.display = 'none';
  }

  showSuggestions() {
    document.getElementById('quickSuggestions').style.display = 'flex';
  }

  hideSuggestions() {
    document.getElementById('quickSuggestions').style.display = 'none';
  }

  scrollToBottom() {
    const messagesContainer = document.getElementById('chatMessages');
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }

  getCustomerInfo() {
    // يمكن تخصيص هذه الدالة لجلب معلومات العميل من المتجر
    return {
      name: localStorage.getItem('customer_name'),
      email: localStorage.getItem('customer_email'),
      phone: localStorage.getItem('customer_phone')
    };
  }

  showFeedbackModal() {
    document.getElementById('feedbackModal').style.display = 'flex';
  }

  closeFeedbackModal() {
    document.getElementById('feedbackModal').style.display = 'none';
  }

  setRating(rating) {
    this.currentRating = rating;
    const stars = document.querySelectorAll('.rating-stars i');
    stars.forEach((star, index) => {
      if (index < rating) {
        star.classList.add('active');
      } else {
        star.classList.remove('active');
      }
    });
  }

  async submitFeedback() {
    const feedbackText = document.getElementById('feedbackText').value;

    try {
      await fetch(`${this.config.apiUrl}/chatbot/conversation/${this.sessionId}/end`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          storeId: this.config.storeId,
          feedback: {
            rating: this.currentRating,
            comment: feedbackText
          }
        })
      });
    } catch (error) {
      console.error('فشل في إرسال التقييم:', error);
    }

    this.closeFeedbackModal();
  }

  handleFileUpload(file) {
    if (!file) return;

    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      alert('نوع الملف غير مدعوم');
      return;
    }

    // التحقق من حجم الملف (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('حجم الملف كبير جداً');
      return;
    }

    // رفع الملف (يمكن تطوير هذه الوظيفة لاحقاً)
    console.log('رفع ملف:', file.name);
  }

  playNotificationSound() {
    // تشغيل صوت إشعار بسيط
    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
    audio.volume = 0.1;
    audio.play().catch(() => {
      // تجاهل الأخطاء إذا لم يتمكن من تشغيل الصوت
    });
  }
}

// تهيئة الأداة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  window.sallaChat = new SallaChatWidget();
});
