/* الخطوط والمتغيرات */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background-color: #ffffff;
  --surface-color: #f8fafc;
  --border-color: #e2e8f0;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* إعادة تعيين الأساسيات */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  direction: rtl;
  text-align: right;
}

/* زر فتح الدردشة */
.chat-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
  z-index: 1000;
  color: white;
  font-size: 24px;
}

.chat-toggle:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

.chat-toggle .notification-badge {
  position: absolute;
  top: -5px;
  left: -5px;
  background: var(--error-color);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

/* نافذة الدردشة */
.chat-widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 380px;
  height: 600px;
  background: var(--background-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  display: none;
  flex-direction: column;
  z-index: 1001;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.chat-widget.open {
  display: flex;
  animation: slideUp 0.3s ease-out;
}

.chat-widget.minimized {
  height: 60px;
}

.chat-widget.minimized .chat-messages,
.chat-widget.minimized .quick-suggestions,
.chat-widget.minimized .chat-input-area {
  display: none;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* رأس الدردشة */
.chat-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.chat-header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bot-avatar {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.bot-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
}

.status {
  font-size: 12px;
  opacity: 0.9;
}

.status.online::before {
  content: '●';
  color: var(--success-color);
  margin-left: 4px;
}

.chat-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* منطقة الرسائل */
.chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: var(--surface-color);
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chat-messages::-webkit-scrollbar {
  width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 2px;
}

/* الرسائل */
.message {
  display: flex;
  gap: 8px;
  max-width: 85%;
}

.message.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.bot-message .message-avatar {
  background: var(--primary-color);
  color: white;
}

.user-message .message-avatar {
  background: var(--secondary-color);
  color: white;
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: var(--radius-lg);
  line-height: 1.5;
  word-wrap: break-word;
}

.bot-message .message-bubble {
  background: white;
  border: 1px solid var(--border-color);
  border-bottom-right-radius: var(--radius-sm);
}

.user-message .message-bubble {
  background: var(--primary-color);
  color: white;
  border-bottom-left-radius: var(--radius-sm);
}

.message-time {
  font-size: 11px;
  color: var(--text-secondary);
  padding: 0 4px;
}

/* منتجات مقترحة */
.suggested-products {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  overflow-x: auto;
  padding: 4px 0;
}

.product-card {
  min-width: 120px;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.product-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.product-image {
  width: 100%;
  height: 60px;
  background: var(--surface-color);
  border-radius: var(--radius-sm);
  margin-bottom: 6px;
  background-size: cover;
  background-position: center;
}

.product-name {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 2px;
  line-height: 1.3;
}

.product-price {
  font-size: 11px;
  color: var(--primary-color);
  font-weight: 600;
}

/* الاقتراحات السريعة */
.quick-suggestions {
  padding: 12px 16px;
  background: white;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-item {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 8px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.suggestion-item:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.suggestion-item i {
  font-size: 12px;
}

/* منطقة كتابة الرسالة */
.chat-input-area {
  background: white;
  border-top: 1px solid var(--border-color);
  padding: 16px;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  font-size: 13px;
  color: var(--text-secondary);
}

.typing-dots {
  display: flex;
  gap: 2px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  background: var(--text-secondary);
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.chat-input-container {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.input-wrapper {
  flex: 1;
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 12px;
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.input-wrapper:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

#messageInput {
  flex: 1;
  border: none;
  background: transparent;
  resize: none;
  outline: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  max-height: 100px;
}

#messageInput::placeholder {
  color: var(--text-secondary);
}

.input-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.action-btn:hover {
  color: var(--primary-color);
  background: rgba(37, 99, 235, 0.1);
}

.send-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-btn:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.send-btn:disabled {
  background: var(--secondary-color);
  cursor: not-allowed;
  opacity: 0.5;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 11px;
  color: var(--text-secondary);
}

/* نافذة التقييم */
.feedback-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1002;
}

.feedback-content {
  background: white;
  border-radius: var(--radius-xl);
  padding: 24px;
  max-width: 400px;
  width: 90%;
  text-align: center;
}

.feedback-content h3 {
  margin-bottom: 8px;
  color: var(--text-primary);
}

.feedback-content p {
  color: var(--text-secondary);
  margin-bottom: 20px;
}

.rating-stars {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.rating-stars i {
  font-size: 24px;
  color: var(--border-color);
  cursor: pointer;
  transition: color 0.2s ease;
}

.rating-stars i:hover,
.rating-stars i.active {
  color: var(--warning-color);
}

#feedbackText {
  width: 100%;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 12px;
  margin-bottom: 16px;
  resize: vertical;
  font-family: inherit;
}

.feedback-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.btn-primary, .btn-secondary {
  padding: 10px 20px;
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-dark);
}

.btn-secondary {
  background: var(--surface-color);
  color: var(--text-secondary);
}

.btn-secondary:hover {
  background: var(--border-color);
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 480px) {
  .chat-widget {
    width: 100vw;
    height: 100vh;
    bottom: 0;
    right: 0;
    border-radius: 0;
  }
  
  .chat-toggle {
    bottom: 16px;
    right: 16px;
  }
}
