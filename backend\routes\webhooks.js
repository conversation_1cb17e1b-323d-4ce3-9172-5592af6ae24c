const express = require('express');
const router = express.Router();
const crypto = require('crypto');
const database = require('../config/database');
const logger = require('../config/logger');

// التحقق من صحة webhook
function verifyWebhookSignature(payload, signature, secret) {
  if (!signature || !secret) {
    return false;
  }

  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');

  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}

// معالجة webhooks من سلة
router.post('/salla', async (req, res) => {
  try {
    const signature = req.headers['x-salla-signature'];
    const payload = JSON.stringify(req.body);
    const event = req.body;

    logger.logWebhook('استلام webhook', event.merchant?.id, 'received', event);

    // التحقق من التوقيع (إذا كان متوفراً)
    const webhookSecret = process.env.WEBHOOK_SECRET;
    if (webhookSecret && signature) {
      const isValid = verifyWebhookSignature(payload, signature, webhookSecret);
      if (!isValid) {
        logger.logWarning('توقيع webhook غير صحيح');
        return res.status(401).json({ error: 'توقيع غير صحيح' });
      }
    }

    // حفظ webhook في قاعدة البيانات
    const storeId = event.merchant?.id || 'unknown';
    await database.run(
      `INSERT INTO webhook_logs (store_id, event_type, payload, status, created_at)
       VALUES (?, ?, ?, 'received', CURRENT_TIMESTAMP)`,
      [storeId, event.event, payload]
    );

    // معالجة الحدث حسب نوعه
    await processWebhookEvent(event);

    logger.logWebhook('تمت معالجة webhook', storeId, 'processed', { event: event.event });

    res.status(200).json({ status: 'success' });

  } catch (error) {
    logger.logError('فشل في معالجة webhook', error);
    res.status(500).json({ error: 'خطأ في معالجة الحدث' });
  }
});

// معالجة أحداث webhook
async function processWebhookEvent(event) {
  const { event: eventType, data, merchant } = event;
  const storeId = merchant?.id;

  if (!storeId) {
    logger.logWarning('معرف المتجر غير متوفر في webhook');
    return;
  }

  try {
    switch (eventType) {
      case 'order.created':
        await handleOrderCreated(storeId, data);
        break;

      case 'order.updated':
        await handleOrderUpdated(storeId, data);
        break;

      case 'product.created':
        await handleProductCreated(storeId, data);
        break;

      case 'product.updated':
        await handleProductUpdated(storeId, data);
        break;

      case 'customer.created':
        await handleCustomerCreated(storeId, data);
        break;

      case 'app.store.authorize':
        await handleStoreAuthorize(storeId, data);
        break;

      case 'app.installed':
        await handleAppInstalled(storeId, data);
        break;

      default:
        logger.logWarning(`نوع حدث غير مدعوم: ${eventType}`);
    }

    // تحديث حالة webhook
    await database.run(
      `UPDATE webhook_logs 
       SET status = 'processed', processed_at = CURRENT_TIMESTAMP 
       WHERE store_id = ? AND event_type = ? AND status = 'received'
       ORDER BY created_at DESC LIMIT 1`,
      [storeId, eventType]
    );

  } catch (error) {
    logger.logError(`فشل في معالجة حدث ${eventType}`, error);
    
    // تحديث حالة webhook كفاشل
    await database.run(
      `UPDATE webhook_logs 
       SET status = 'failed', response = ?, processed_at = CURRENT_TIMESTAMP 
       WHERE store_id = ? AND event_type = ? AND status = 'received'
       ORDER BY created_at DESC LIMIT 1`,
      [error.message, storeId, eventType]
    );
  }
}

// معالجة إنشاء طلب جديد
async function handleOrderCreated(storeId, orderData) {
  logger.logStart('معالجة طلب جديد', { storeId, orderId: orderData.id });

  // تسجيل الحدث في التحليلات
  await database.run(
    `INSERT INTO analytics (store_id, event_type, event_data, created_at)
     VALUES (?, 'order_created', ?, CURRENT_TIMESTAMP)`,
    [storeId, JSON.stringify({
      orderId: orderData.id,
      amount: orderData.total?.amount,
      currency: orderData.total?.currency,
      itemsCount: orderData.items?.length || 0
    })]
  );

  logger.logSuccess('تمت معالجة الطلب الجديد');
}

// معالجة تحديث طلب
async function handleOrderUpdated(storeId, orderData) {
  logger.logStart('معالجة تحديث طلب', { storeId, orderId: orderData.id });

  // تسجيل الحدث في التحليلات
  await database.run(
    `INSERT INTO analytics (store_id, event_type, event_data, created_at)
     VALUES (?, 'order_updated', ?, CURRENT_TIMESTAMP)`,
    [storeId, JSON.stringify({
      orderId: orderData.id,
      status: orderData.status,
      updatedAt: orderData.updated_at
    })]
  );

  logger.logSuccess('تمت معالجة تحديث الطلب');
}

// معالجة إنشاء منتج جديد
async function handleProductCreated(storeId, productData) {
  logger.logStart('معالجة منتج جديد', { storeId, productId: productData.id });

  // حفظ المنتج في الكاش
  await database.run(
    `INSERT OR REPLACE INTO products_cache (store_id, product_id, product_data, last_updated)
     VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
    [storeId, productData.id, JSON.stringify(productData)]
  );

  // تسجيل الحدث في التحليلات
  await database.run(
    `INSERT INTO analytics (store_id, event_type, event_data, created_at)
     VALUES (?, 'product_created', ?, CURRENT_TIMESTAMP)`,
    [storeId, JSON.stringify({
      productId: productData.id,
      name: productData.name,
      price: productData.price
    })]
  );

  logger.logSuccess('تمت معالجة المنتج الجديد');
}

// معالجة تحديث منتج
async function handleProductUpdated(storeId, productData) {
  logger.logStart('معالجة تحديث منتج', { storeId, productId: productData.id });

  // تحديث المنتج في الكاش
  await database.run(
    `INSERT OR REPLACE INTO products_cache (store_id, product_id, product_data, last_updated)
     VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
    [storeId, productData.id, JSON.stringify(productData)]
  );

  // تسجيل الحدث في التحليلات
  await database.run(
    `INSERT INTO analytics (store_id, event_type, event_data, created_at)
     VALUES (?, 'product_updated', ?, CURRENT_TIMESTAMP)`,
    [storeId, JSON.stringify({
      productId: productData.id,
      name: productData.name,
      updatedAt: productData.updated_at
    })]
  );

  logger.logSuccess('تمت معالجة تحديث المنتج');
}

// معالجة إنشاء عميل جديد
async function handleCustomerCreated(storeId, customerData) {
  logger.logStart('معالجة عميل جديد', { storeId, customerId: customerData.id });

  // تسجيل الحدث في التحليلات
  await database.run(
    `INSERT INTO analytics (store_id, event_type, event_data, created_at)
     VALUES (?, 'customer_created', ?, CURRENT_TIMESTAMP)`,
    [storeId, JSON.stringify({
      customerId: customerData.id,
      email: customerData.email,
      phone: customerData.mobile
    })]
  );

  logger.logSuccess('تمت معالجة العميل الجديد');
}

// معالجة تفويض المتجر
async function handleStoreAuthorize(storeId, data) {
  logger.logStart('معالجة تفويض متجر', { storeId });

  // تحديث حالة المتجر
  await database.run(
    `UPDATE stores 
     SET is_active = 1, updated_at = CURRENT_TIMESTAMP 
     WHERE store_id = ?`,
    [storeId]
  );

  logger.logSuccess('تمت معالجة تفويض المتجر');
}

// معالجة تثبيت التطبيق
async function handleAppInstalled(storeId, data) {
  logger.logStart('معالجة تثبيت تطبيق', { storeId });

  // تسجيل الحدث في التحليلات
  await database.run(
    `INSERT INTO analytics (store_id, event_type, event_data, created_at)
     VALUES (?, 'app_installed', ?, CURRENT_TIMESTAMP)`,
    [storeId, JSON.stringify(data)]
  );

  logger.logSuccess('تمت معالجة تثبيت التطبيق');
}

// جلب سجل webhooks
router.get('/logs/:storeId', async (req, res) => {
  try {
    const { storeId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    const logs = await database.all(
      `SELECT * FROM webhook_logs 
       WHERE store_id = ? 
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`,
      [storeId, limit, offset]
    );

    const total = await database.get(
      'SELECT COUNT(*) as count FROM webhook_logs WHERE store_id = ?',
      [storeId]
    );

    res.json({
      success: true,
      data: {
        logs: logs,
        pagination: {
          page: page,
          limit: limit,
          total: total.count,
          pages: Math.ceil(total.count / limit)
        }
      }
    });

  } catch (error) {
    logger.logError('فشل في جلب سجل webhooks', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب السجل'
    });
  }
});

// إعادة معالجة webhook فاشل
router.post('/retry/:logId', async (req, res) => {
  try {
    const { logId } = req.params;

    const log = await database.get(
      'SELECT * FROM webhook_logs WHERE id = ?',
      [logId]
    );

    if (!log) {
      return res.status(404).json({
        success: false,
        message: 'السجل غير موجود'
      });
    }

    const event = JSON.parse(log.payload);
    await processWebhookEvent(event);

    res.json({
      success: true,
      message: 'تمت إعادة معالجة الحدث بنجاح'
    });

  } catch (error) {
    logger.logError('فشل في إعادة معالجة webhook', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في إعادة المعالجة'
    });
  }
});

module.exports = router;
