const express = require('express');
const router = express.Router();
const aiService = require('../services/aiService');
const database = require('../config/database');
const logger = require('../config/logger');
const { body, validationResult } = require('express-validator');

// التحقق من صحة البيانات
const validateChatMessage = [
  body('message').notEmpty().withMessage('الرسالة مطلوبة'),
  body('storeId').notEmpty().withMessage('معرف المتجر مطلوب'),
  body('sessionId').optional().isString(),
  body('customerInfo').optional().isObject()
];

// إرسال رسالة للروبوت
router.post('/chat', validateChatMessage, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { message, storeId, sessionId, customerInfo } = req.body;
    
    logger.logChatEvent('رسالة جديدة', sessionId, { storeId, messageLength: message.length });

    // التحقق من وجود المتجر
    const store = await database.get(
      'SELECT * FROM stores WHERE store_id = ? AND is_active = 1',
      [storeId]
    );

    if (!store) {
      return res.status(404).json({
        success: false,
        message: 'المتجر غير موجود أو غير نشط'
      });
    }

    // البحث عن المحادثة أو إنشاء جديدة
    let conversation = await database.get(
      'SELECT * FROM conversations WHERE session_id = ? AND store_id = ?',
      [sessionId, storeId]
    );

    if (!conversation) {
      const result = await database.run(
        `INSERT INTO conversations (store_id, session_id, customer_name, customer_email, customer_phone, status)
         VALUES (?, ?, ?, ?, ?, 'active')`,
        [
          storeId,
          sessionId,
          customerInfo?.name || null,
          customerInfo?.email || null,
          customerInfo?.phone || null
        ]
      );
      
      conversation = { id: result.id, store_id: storeId, session_id: sessionId };
    }

    // حفظ رسالة العميل
    await database.run(
      `INSERT INTO messages (conversation_id, sender_type, message_text, message_type)
       VALUES (?, 'customer', ?, 'text')`,
      [conversation.id, message]
    );

    // جلب الرسائل السابقة للسياق
    const previousMessages = await database.all(
      `SELECT message_text, sender_type FROM messages 
       WHERE conversation_id = ? 
       ORDER BY created_at DESC 
       LIMIT 5`,
      [conversation.id]
    );

    // بناء السياق
    const context = {
      customerName: customerInfo?.name,
      previousMessages: previousMessages.map(m => m.message_text),
      sessionId: sessionId
    };

    // إنشاء الرد الذكي
    const aiResponse = await aiService.generateChatResponse(storeId, message, context);

    // حفظ رد الروبوت
    await database.run(
      `INSERT INTO messages (conversation_id, sender_type, message_text, message_type, metadata)
       VALUES (?, 'bot', ?, ?, ?)`,
      [
        conversation.id,
        aiResponse.message,
        aiResponse.type,
        JSON.stringify({
          suggestions: aiResponse.suggestions,
          products: aiResponse.products
        })
      ]
    );

    // تحليل المشاعر (اختياري)
    const sentiment = await aiService.analyzeSentiment(message);

    // تسجيل الحدث في التحليلات
    await database.run(
      `INSERT INTO analytics (store_id, event_type, event_data, user_session)
       VALUES (?, 'chat_message', ?, ?)`,
      [
        storeId,
        JSON.stringify({
          messageLength: message.length,
          responseTime: Date.now(),
          sentiment: sentiment
        }),
        sessionId
      ]
    );

    logger.logChatEvent('تم إنشاء الرد', sessionId, { 
      storeId, 
      responseLength: aiResponse.message.length 
    });

    res.json({
      success: true,
      data: {
        message: aiResponse.message,
        type: aiResponse.type,
        suggestions: aiResponse.suggestions,
        products: aiResponse.products,
        conversationId: conversation.id,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.logError('فشل في معالجة رسالة الدردشة', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في معالجة رسالتك',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// جلب تاريخ المحادثة
router.get('/conversation/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { storeId } = req.query;

    if (!storeId) {
      return res.status(400).json({
        success: false,
        message: 'معرف المتجر مطلوب'
      });
    }

    const conversation = await database.get(
      'SELECT * FROM conversations WHERE session_id = ? AND store_id = ?',
      [sessionId, storeId]
    );

    if (!conversation) {
      return res.json({
        success: true,
        data: {
          messages: [],
          conversation: null
        }
      });
    }

    const messages = await database.all(
      `SELECT * FROM messages 
       WHERE conversation_id = ? 
       ORDER BY created_at ASC`,
      [conversation.id]
    );

    // تحويل البيانات الوصفية من JSON
    const formattedMessages = messages.map(msg => ({
      ...msg,
      metadata: msg.metadata ? JSON.parse(msg.metadata) : {}
    }));

    res.json({
      success: true,
      data: {
        conversation: conversation,
        messages: formattedMessages
      }
    });

  } catch (error) {
    logger.logError('فشل في جلب تاريخ المحادثة', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب تاريخ المحادثة'
    });
  }
});

// إنهاء المحادثة
router.post('/conversation/:sessionId/end', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { storeId, feedback } = req.body;

    await database.run(
      `UPDATE conversations 
       SET status = 'ended', updated_at = CURRENT_TIMESTAMP 
       WHERE session_id = ? AND store_id = ?`,
      [sessionId, storeId]
    );

    // حفظ التقييم إذا تم تقديمه
    if (feedback) {
      await database.run(
        `INSERT INTO analytics (store_id, event_type, event_data, user_session)
         VALUES (?, 'conversation_feedback', ?, ?)`,
        [storeId, JSON.stringify(feedback), sessionId]
      );
    }

    logger.logChatEvent('انتهاء المحادثة', sessionId, { storeId, feedback });

    res.json({
      success: true,
      message: 'تم إنهاء المحادثة بنجاح'
    });

  } catch (error) {
    logger.logError('فشل في إنهاء المحادثة', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في إنهاء المحادثة'
    });
  }
});

// جلب الاقتراحات السريعة
router.get('/suggestions/:storeId', async (req, res) => {
  try {
    const { storeId } = req.params;

    // اقتراحات افتراضية
    const defaultSuggestions = [
      'مرحباً، كيف يمكنني مساعدتك؟',
      'أريد البحث عن منتج',
      'ما هي طرق الدفع المتاحة؟',
      'معلومات عن الشحن والتوصيل',
      'أريد التحدث مع خدمة العملاء'
    ];

    // يمكن تخصيص الاقتراحات بناءً على إعدادات المتجر
    const customSuggestions = await database.all(
      `SELECT setting_value FROM bot_settings 
       WHERE store_id = ? AND setting_key = 'quick_suggestions'`,
      [storeId]
    );

    const suggestions = customSuggestions.length > 0 
      ? JSON.parse(customSuggestions[0].setting_value)
      : defaultSuggestions;

    res.json({
      success: true,
      data: suggestions
    });

  } catch (error) {
    logger.logError('فشل في جلب الاقتراحات', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الاقتراحات'
    });
  }
});

// إنشاء محتوى SEO
router.post('/seo/generate', async (req, res) => {
  try {
    const { storeId, type, data } = req.body;

    if (!storeId || !type || !data) {
      return res.status(400).json({
        success: false,
        message: 'البيانات المطلوبة غير مكتملة'
      });
    }

    const content = await aiService.generateSEOContent(storeId, type, data);

    res.json({
      success: true,
      data: {
        content: content,
        type: type,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.logError('فشل في إنشاء محتوى SEO', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في إنشاء المحتوى'
    });
  }
});

module.exports = router;
