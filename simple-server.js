// خادم مبسط للاختبار
const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

// إنشاء المجلدات إذا لم تكن موجودة
const folders = ['database', 'logs', 'uploads'];
folders.forEach(folder => {
  if (!fs.existsSync(folder)) {
    fs.mkdirSync(folder, { recursive: true });
    console.log(`✅ تم إنشاء مجلد: ${folder}`);
  }
});

const server = http.createServer((req, res) => {
  // إعداد CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  const url = req.url;
  
  if (url === '/') {
    // الصفحة الرئيسية
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
      <!DOCTYPE html>
      <html lang="ar" dir="rtl">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام تكامل الذكاء الاصطناعي مع سلة</title>
        <style>
          body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f2f5; }
          .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
          h1 { color: #2563eb; margin-bottom: 20px; }
          .status { background: #dcfce7; color: #166534; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .links { margin-top: 30px; }
          .link { display: inline-block; margin: 10px; padding: 12px 24px; background: #2563eb; color: white; text-decoration: none; border-radius: 5px; }
          .link:hover { background: #1d4ed8; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🤖 نظام تكامل الذكاء الاصطناعي مع سلة</h1>
          <div class="status">
            ✅ الخادم يعمل بنجاح!
          </div>
          <p>مرحباً بك في نظام تكامل الذكاء الاصطناعي مع متاجر سلة</p>
          <div class="links">
            <a href="/admin" class="link">👨‍💼 لوحة الإدارة</a>
            <a href="/widget" class="link">🤖 أداة الدردشة</a>
            <a href="/api" class="link">📊 API</a>
          </div>
        </div>
      </body>
      </html>
    `);
  } else if (url === '/admin') {
    // لوحة الإدارة
    const adminPath = path.join(__dirname, 'frontend', 'admin', 'index.html');
    if (fs.existsSync(adminPath)) {
      const content = fs.readFileSync(adminPath, 'utf8');
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(content);
    } else {
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end('<h1>لوحة الإدارة قيد التطوير</h1><p><a href="/">العودة للرئيسية</a></p>');
    }
  } else if (url === '/widget') {
    // أداة الدردشة
    const widgetPath = path.join(__dirname, 'frontend', 'widget', 'index.html');
    if (fs.existsSync(widgetPath)) {
      const content = fs.readFileSync(widgetPath, 'utf8');
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(content);
    } else {
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end('<h1>أداة الدردشة قيد التطوير</h1><p><a href="/">العودة للرئيسية</a></p>');
    }
  } else if (url === '/api') {
    // API info
    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
    res.end(JSON.stringify({
      message: 'مرحباً بك في API نظام تكامل الذكاء الاصطناعي مع سلة',
      version: '1.0.0',
      status: 'running',
      endpoints: {
        admin: '/admin',
        widget: '/widget',
        api: '/api'
      }
    }, null, 2));
  } else {
    // 404
    res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end('<h1>404 - الصفحة غير موجودة</h1><p><a href="/">العودة للرئيسية</a></p>');
  }
});

server.listen(PORT, () => {
  console.log('🚀 الخادم يعمل بنجاح!');
  console.log(`🌐 الرابط: http://localhost:${PORT}`);
  console.log(`👨‍💼 لوحة الإدارة: http://localhost:${PORT}/admin`);
  console.log(`🤖 أداة الدردشة: http://localhost:${PORT}/widget`);
  console.log('');
  console.log('اضغط Ctrl+C لإيقاف الخادم');
});

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 تم إيقاف الخادم');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 تم إيقاف الخادم');
  process.exit(0);
});
