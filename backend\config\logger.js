const winston = require('winston');
const path = require('path');
const fs = require('fs');

// إنشاء مجلد السجلات إذا لم يكن موجوداً
const logDir = './logs';
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// تنسيق السجلات باللغة العربية
const arabicFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ level, message, timestamp, stack }) => {
    const logMessage = stack || message;
    return `[${timestamp}] ${level.toUpperCase()}: ${logMessage}`;
  })
);

// إعداد مستويات السجلات
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// إنشاء مسجل السجلات
const logger = winston.createLogger({
  levels: logLevels,
  level: process.env.LOG_LEVEL || 'info',
  format: arabicFormat,
  defaultMeta: { service: 'salla-ai-integration' },
  transports: [
    // سجل الأخطاء
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),

    // سجل جميع العمليات
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),

    // سجل العمليات اليومي
    new winston.transports.File({
      filename: path.join(logDir, `app-${new Date().toISOString().split('T')[0]}.log`),
      maxsize: 5242880, // 5MB
      maxFiles: 30,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  ],

  // معالجة الاستثناءات غير المعالجة
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'exceptions.log'),
      maxsize: 5242880,
      maxFiles: 5
    })
  ],

  // معالجة الرفض غير المعالج للوعود
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'rejections.log'),
      maxsize: 5242880,
      maxFiles: 5
    })
  ]
});

// إضافة سجل وحدة التحكم في بيئة التطوير
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
      winston.format.printf(({ level, message, timestamp }) => {
        return `${timestamp} ${level}: ${message}`;
      })
    )
  }));
}

// دوال مساعدة للتسجيل
const loggerHelpers = {
  // تسجيل بداية العملية
  logStart: (operation, data = {}) => {
    logger.info(`🚀 بداية العملية: ${operation}`, { operation, data });
  },

  // تسجيل نهاية العملية بنجاح
  logSuccess: (operation, data = {}) => {
    logger.info(`✅ تمت العملية بنجاح: ${operation}`, { operation, data });
  },

  // تسجيل فشل العملية
  logError: (operation, error, data = {}) => {
    logger.error(`❌ فشلت العملية: ${operation}`, { 
      operation, 
      error: error.message, 
      stack: error.stack, 
      data 
    });
  },

  // تسجيل تحذير
  logWarning: (message, data = {}) => {
    logger.warn(`⚠️ تحذير: ${message}`, { data });
  },

  // تسجيل معلومات API
  logApiCall: (method, url, status, duration, data = {}) => {
    logger.info(`🌐 API Call: ${method} ${url} - Status: ${status} - Duration: ${duration}ms`, {
      method,
      url,
      status,
      duration,
      data
    });
  },

  // تسجيل أنشطة المستخدم
  logUserActivity: (userId, action, details = {}) => {
    logger.info(`👤 نشاط المستخدم: ${userId} - ${action}`, {
      userId,
      action,
      details
    });
  },

  // تسجيل أحداث الويب هوك
  logWebhook: (event, storeId, status, data = {}) => {
    logger.info(`🔗 Webhook: ${event} - Store: ${storeId} - Status: ${status}`, {
      event,
      storeId,
      status,
      data
    });
  },

  // تسجيل أحداث الدردشة
  logChatEvent: (event, conversationId, details = {}) => {
    logger.info(`💬 Chat Event: ${event} - Conversation: ${conversationId}`, {
      event,
      conversationId,
      details
    });
  }
};

// دمج المسجل مع الدوال المساعدة
Object.assign(logger, loggerHelpers);

module.exports = logger;
