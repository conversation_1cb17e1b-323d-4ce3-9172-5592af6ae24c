const rateLimit = require('express-rate-limit');
const logger = require('../config/logger');

// محدد المعدل العام
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 100, // حد أقصى 100 طلب لكل IP
  message: {
    success: false,
    message: 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً',
    retryAfter: '15 دقيقة'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.logWarning('تم تجاوز حد المعدل العام', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path
    });
    
    res.status(429).json({
      success: false,
      message: 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً',
      retryAfter: '15 دقيقة'
    });
  }
});

// محدد المعدل للمصادقة
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 5, // حد أقصى 5 محاولات تسجيل دخول
  message: {
    success: false,
    message: 'تم تجاوز عدد محاولات تسجيل الدخول، يرجى المحاولة بعد 15 دقيقة',
    retryAfter: '15 دقيقة'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // لا نحسب الطلبات الناجحة
  handler: (req, res) => {
    logger.logWarning('تم تجاوز حد محاولات تسجيل الدخول', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      username: req.body?.username
    });
    
    res.status(429).json({
      success: false,
      message: 'تم تجاوز عدد محاولات تسجيل الدخول، يرجى المحاولة بعد 15 دقيقة',
      retryAfter: '15 دقيقة'
    });
  }
});

// محدد المعدل للدردشة
const chatLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // دقيقة واحدة
  max: 30, // حد أقصى 30 رسالة في الدقيقة
  message: {
    success: false,
    message: 'تم إرسال رسائل كثيرة، يرجى الانتظار قليلاً',
    retryAfter: '1 دقيقة'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // استخدام session ID بدلاً من IP للدردشة
    return req.body?.sessionId || req.ip;
  },
  handler: (req, res) => {
    logger.logWarning('تم تجاوز حد رسائل الدردشة', {
      sessionId: req.body?.sessionId,
      ip: req.ip,
      storeId: req.body?.storeId
    });
    
    res.status(429).json({
      success: false,
      message: 'تم إرسال رسائل كثيرة، يرجى الانتظار قليلاً',
      retryAfter: '1 دقيقة'
    });
  }
});

// محدد المعدل لـ API
const apiLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // دقيقة واحدة
  max: 60, // حد أقصى 60 طلب API في الدقيقة
  message: {
    success: false,
    message: 'تم تجاوز حد طلبات API، يرجى المحاولة لاحقاً',
    retryAfter: '1 دقيقة'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.logWarning('تم تجاوز حد طلبات API', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
      method: req.method
    });
    
    res.status(429).json({
      success: false,
      message: 'تم تجاوز حد طلبات API، يرجى المحاولة لاحقاً',
      retryAfter: '1 دقيقة'
    });
  }
});

// محدد المعدل للتحميل
const uploadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 10, // حد أقصى 10 تحميلات
  message: {
    success: false,
    message: 'تم تجاوز حد التحميلات، يرجى المحاولة لاحقاً',
    retryAfter: '15 دقيقة'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.logWarning('تم تجاوز حد التحميلات', {
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    res.status(429).json({
      success: false,
      message: 'تم تجاوز حد التحميلات، يرجى المحاولة لاحقاً',
      retryAfter: '15 دقيقة'
    });
  }
});

// محدد معدل مخصص للمسارات الحساسة
const strictLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // ساعة واحدة
  max: 10, // حد أقصى 10 طلبات في الساعة
  message: {
    success: false,
    message: 'تم تجاوز الحد المسموح للعمليات الحساسة، يرجى المحاولة لاحقاً',
    retryAfter: '1 ساعة'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.logWarning('تم تجاوز حد العمليات الحساسة', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path
    });
    
    res.status(429).json({
      success: false,
      message: 'تم تجاوز الحد المسموح للعمليات الحساسة، يرجى المحاولة لاحقاً',
      retryAfter: '1 ساعة'
    });
  }
});

// دالة لإنشاء محدد معدل مخصص
const createCustomLimiter = (options) => {
  return rateLimit({
    windowMs: options.windowMs || 15 * 60 * 1000,
    max: options.max || 100,
    message: options.message || {
      success: false,
      message: 'تم تجاوز الحد المسموح من الطلبات'
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: options.keyGenerator,
    handler: options.handler || ((req, res) => {
      res.status(429).json(options.message);
    })
  });
};

module.exports = {
  generalLimiter,
  authLimiter,
  chatLimiter,
  apiLimiter,
  uploadLimiter,
  strictLimiter,
  createCustomLimiter
};
