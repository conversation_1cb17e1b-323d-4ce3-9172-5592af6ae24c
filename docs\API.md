# 📚 وثائق API

## 🌐 نظرة عامة

يوفر نظام تكامل الذكاء الاصطناعي مع سلة مجموعة شاملة من APIs لإدارة المحادثات، المتاجر، والتحليلات.

## 🔗 الرابط الأساسي

```
http://localhost:3000/api
```

## 🔐 المصادقة

يستخدم النظام JWT (JSON Web Tokens) للمصادقة. يجب إرسال الرمز في header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## 📋 فهرس APIs

### 🔑 المصادقة والتفويض
- [POST /auth/register](#تسجيل-مستخدم-جديد)
- [POST /auth/login](#تسجيل-الدخول)
- [GET /auth/verify](#التحقق-من-الرمز)
- [POST /auth/logout](#تسجيل-الخروج)

### 🤖 روبوت الدردشة
- [POST /chatbot/chat](#إرسال-رسالة-للروبوت)
- [GET /chatbot/conversation/:sessionId](#جلب-تاريخ-المحادثة)
- [POST /chatbot/conversation/:sessionId/end](#إنهاء-المحادثة)
- [GET /chatbot/suggestions/:storeId](#جلب-الاقتراحات)

### 🏪 إدارة المتاجر
- [GET /salla/auth/url](#رابط-التفويض)
- [GET /salla/auth/callback](#معالجة-التفويض)
- [GET /salla/stores](#جلب-المتاجر)
- [GET /salla/store/:storeId](#معلومات-المتجر)
- [GET /salla/store/:storeId/products](#جلب-المنتجات)

### 👨‍💼 لوحة الإدارة
- [GET /admin/dashboard](#لوحة-المعلومات)
- [GET /admin/conversations](#إدارة-المحادثات)
- [GET /admin/analytics/conversations](#إحصائيات-المحادثات)

---

## 🔑 APIs المصادقة

### تسجيل مستخدم جديد

```http
POST /api/auth/register
```

**Body:**
```json
{
  "username": "admin",
  "email": "<EMAIL>",
  "password": "password123",
  "fullName": "المدير العام"
}
```

**Response:**
```json
{
  "success": true,
  "message": "تم إنشاء الحساب بنجاح",
  "data": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "fullName": "المدير العام"
  }
}
```

### تسجيل الدخول

```http
POST /api/auth/login
```

**Body:**
```json
{
  "username": "admin",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "تم تسجيل الدخول بنجاح",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "fullName": "المدير العام",
      "role": "admin"
    }
  }
}
```

### التحقق من الرمز

```http
GET /api/auth/verify
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "fullName": "المدير العام",
      "role": "admin"
    }
  }
}
```

---

## 🤖 APIs روبوت الدردشة

### إرسال رسالة للروبوت

```http
POST /api/chatbot/chat
```

**Body:**
```json
{
  "message": "مرحباً، أريد البحث عن منتج",
  "storeId": "store_123",
  "sessionId": "session_456",
  "customerInfo": {
    "name": "أحمد محمد",
    "email": "<EMAIL>",
    "phone": "+966501234567"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "مرحباً أحمد! سأساعدك في البحث عن المنتج المناسب. ما نوع المنتج الذي تبحث عنه؟",
    "type": "text",
    "suggestions": [
      "إلكترونيات",
      "ملابس",
      "منزل ومطبخ",
      "رياضة"
    ],
    "products": [],
    "conversationId": 123,
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### جلب تاريخ المحادثة

```http
GET /api/chatbot/conversation/session_456?storeId=store_123
```

**Response:**
```json
{
  "success": true,
  "data": {
    "conversation": {
      "id": 123,
      "store_id": "store_123",
      "session_id": "session_456",
      "customer_name": "أحمد محمد",
      "status": "active",
      "created_at": "2024-01-15T10:00:00Z"
    },
    "messages": [
      {
        "id": 1,
        "sender_type": "customer",
        "message_text": "مرحباً",
        "created_at": "2024-01-15T10:00:00Z"
      },
      {
        "id": 2,
        "sender_type": "bot",
        "message_text": "مرحباً بك! كيف يمكنني مساعدتك؟",
        "created_at": "2024-01-15T10:00:05Z",
        "metadata": {
          "suggestions": ["البحث عن منتج", "طرق الدفع"]
        }
      }
    ]
  }
}
```

### إنهاء المحادثة

```http
POST /api/chatbot/conversation/session_456/end
```

**Body:**
```json
{
  "storeId": "store_123",
  "feedback": {
    "rating": 5,
    "comment": "خدمة ممتازة"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "تم إنهاء المحادثة بنجاح"
}
```

---

## 🏪 APIs إدارة المتاجر

### رابط التفويض

```http
GET /api/salla/auth/url?redirect_uri=http://localhost:3000/callback
```

**Response:**
```json
{
  "success": true,
  "data": {
    "authUrl": "https://accounts.salla.sa/oauth2/authorize?client_id=...",
    "state": "random_state_string",
    "redirectUri": "http://localhost:3000/callback"
  }
}
```

### جلب المتاجر

```http
GET /api/salla/stores
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "store_id": "store_123",
      "store_name": "متجر الإلكترونيات",
      "store_url": "https://electronics-store.salla.sa",
      "is_active": true,
      "created_at": "2024-01-15T09:00:00Z"
    }
  ]
}
```

### معلومات المتجر

```http
GET /api/salla/store/store_123
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "store_123",
    "name": "متجر الإلكترونيات",
    "domain": "electronics-store.salla.sa",
    "description": "متجر متخصص في الإلكترونيات",
    "logo": "https://cdn.salla.sa/logo.png",
    "currency": "SAR",
    "timezone": "Asia/Riyadh"
  }
}
```

### جلب المنتجات

```http
GET /api/salla/store/store_123/products?page=1&limit=20
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "product_456",
        "name": "آيفون 15 برو",
        "description": "أحدث هاتف من آبل",
        "price": {
          "amount": 4999,
          "currency": "SAR"
        },
        "images": [
          {
            "url": "https://cdn.salla.sa/product1.jpg",
            "alt": "آيفون 15 برو"
          }
        ],
        "status": "active",
        "quantity": 50
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 150,
      "total_pages": 8
    }
  }
}
```

---

## 👨‍💼 APIs لوحة الإدارة

### لوحة المعلومات

```http
GET /api/admin/dashboard
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:**
```json
{
  "success": true,
  "data": {
    "stats": {
      "activeStores": 5,
      "todayConversations": 23,
      "todayMessages": 156,
      "cachedProducts": 1250
    },
    "charts": {
      "weeklyConversations": [
        {"date": "2024-01-09", "count": 15},
        {"date": "2024-01-10", "count": 18},
        {"date": "2024-01-11", "count": 22},
        {"date": "2024-01-12", "count": 19},
        {"date": "2024-01-13", "count": 25},
        {"date": "2024-01-14", "count": 21},
        {"date": "2024-01-15", "count": 23}
      ]
    },
    "recentConversations": [
      {
        "id": 123,
        "customer_name": "أحمد محمد",
        "store_name": "متجر الإلكترونيات",
        "status": "active",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### إدارة المحادثات

```http
GET /api/admin/conversations?page=1&limit=20&storeId=store_123&status=active
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:**
```json
{
  "success": true,
  "data": {
    "conversations": [
      {
        "id": 123,
        "store_id": "store_123",
        "customer_name": "أحمد محمد",
        "customer_email": "<EMAIL>",
        "store_name": "متجر الإلكترونيات",
        "status": "active",
        "message_count": 8,
        "last_message_at": "2024-01-15T10:35:00Z",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "pages": 3
    }
  }
}
```

---

## ⚠️ رموز الأخطاء

| الرمز | الوصف |
|-------|--------|
| 200 | نجح الطلب |
| 400 | بيانات غير صحيحة |
| 401 | غير مصرح |
| 403 | ممنوع |
| 404 | غير موجود |
| 429 | تجاوز الحد المسموح |
| 500 | خطأ في الخادم |

## 📝 أمثلة الاستخدام

### JavaScript/Node.js

```javascript
// تسجيل الدخول
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'admin',
    password: 'password123'
  })
});

const loginData = await loginResponse.json();
const token = loginData.data.token;

// إرسال رسالة للروبوت
const chatResponse = await fetch('/api/chatbot/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    message: 'مرحباً',
    storeId: 'store_123',
    sessionId: 'session_456'
  })
});

const chatData = await chatResponse.json();
console.log(chatData.data.message);
```

### Python

```python
import requests

# تسجيل الدخول
login_response = requests.post('/api/auth/login', json={
    'username': 'admin',
    'password': 'password123'
})

token = login_response.json()['data']['token']

# جلب المتاجر
stores_response = requests.get('/api/salla/stores', headers={
    'Authorization': f'Bearer {token}'
})

stores = stores_response.json()['data']
print(f'عدد المتاجر: {len(stores)}')
```

### cURL

```bash
# تسجيل الدخول
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password123"}'

# إرسال رسالة للروبوت
curl -X POST http://localhost:3000/api/chatbot/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "مرحباً",
    "storeId": "store_123",
    "sessionId": "session_456"
  }'
```

---

## 🔄 معدل الطلبات

| النوع | الحد الأقصى |
|-------|-------------|
| عام | 100 طلب / 15 دقيقة |
| تسجيل الدخول | 5 محاولات / 15 دقيقة |
| الدردشة | 30 رسالة / دقيقة |
| API | 60 طلب / دقيقة |

## 📞 الدعم الفني

للحصول على المساعدة في استخدام APIs:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +966501234567
- 💬 الدردشة المباشرة: متاحة في لوحة الإدارة

---

**تم التطوير بواسطة Augment Agent** 🤖
