const axios = require('axios');
const logger = require('../config/logger');
const database = require('../config/database');

class AIService {
  constructor() {
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.openaiModel = process.env.OPENAI_MODEL || 'gpt-3.5-turbo';
    this.openaiBaseUrl = 'https://api.openai.com/v1';
  }

  // إنشاء رد ذكي للعميل
  async generateChatResponse(storeId, message, context = {}) {
    try {
      logger.logStart('إنشاء رد ذكي', { storeId, messageLength: message.length });

      // جلب معلومات المتجر والمنتجات
      const storeInfo = await this.getStoreContext(storeId);
      
      // بناء السياق للذكاء الاصطناعي
      const systemPrompt = this.buildSystemPrompt(storeInfo, context);
      
      // إجراء طلب للذكاء الاصطناعي
      const response = await this.callOpenAI([
        { role: 'system', content: systemPrompt },
        { role: 'user', content: message }
      ]);

      logger.logSuccess('تم إنشاء الرد الذكي بنجاح');
      
      return {
        message: response,
        type: 'text',
        suggestions: await this.generateSuggestions(storeId, message),
        products: await this.findRelevantProducts(storeId, message)
      };

    } catch (error) {
      logger.logError('فشل في إنشاء الرد الذكي', error);
      return {
        message: 'أعتذر، حدث خطأ تقني. كيف يمكنني مساعدتك بطريقة أخرى؟',
        type: 'text',
        suggestions: ['تصفح المنتجات', 'التواصل مع الدعم', 'معلومات الشحن'],
        products: []
      };
    }
  }

  // بناء السياق للذكاء الاصطناعي
  buildSystemPrompt(storeInfo, context) {
    const basePrompt = `
أنت مساعد ذكي لمتجر إلكتروني على منصة سلة. مهمتك مساعدة العملاء بطريقة ودودة ومفيدة.

معلومات المتجر:
- اسم المتجر: ${storeInfo.name || 'متجر سلة'}
- رابط المتجر: ${storeInfo.url || ''}
- عدد المنتجات: ${storeInfo.productsCount || 0}

إرشادات مهمة:
1. تحدث باللغة العربية دائماً
2. كن ودوداً ومفيداً
3. اقترح منتجات ذات صلة عند الإمكان
4. اسأل أسئلة توضيحية لفهم احتياجات العميل
5. قدم معلومات دقيقة عن الشحن والدفع
6. وجه العملاء للتواصل مع الدعم للمسائل المعقدة
7. استخدم الرموز التعبيرية بشكل مناسب
8. اجعل ردودك قصيرة ومفيدة (أقل من 200 كلمة)

السياق الحالي:
${context.customerName ? `- اسم العميل: ${context.customerName}` : ''}
${context.previousMessages ? `- الرسائل السابقة: ${context.previousMessages.slice(-3).join(', ')}` : ''}
${context.currentPage ? `- الصفحة الحالية: ${context.currentPage}` : ''}
`;

    return basePrompt;
  }

  // استدعاء OpenAI API
  async callOpenAI(messages, maxTokens = 500) {
    if (!this.openaiApiKey) {
      throw new Error('مفتاح OpenAI غير متوفر');
    }

    try {
      const response = await axios.post(
        `${this.openaiBaseUrl}/chat/completions`,
        {
          model: this.openaiModel,
          messages: messages,
          max_tokens: maxTokens,
          temperature: 0.7,
          top_p: 1,
          frequency_penalty: 0,
          presence_penalty: 0
        },
        {
          headers: {
            'Authorization': `Bearer ${this.openaiApiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data.choices[0].message.content.trim();
    } catch (error) {
      logger.logError('فشل في استدعاء OpenAI API', error);
      throw error;
    }
  }

  // جلب سياق المتجر
  async getStoreContext(storeId) {
    try {
      const store = await database.get(
        'SELECT * FROM stores WHERE store_id = ?',
        [storeId]
      );

      if (!store) {
        return { name: 'متجر سلة', url: '', productsCount: 0 };
      }

      // جلب عدد المنتجات المخزنة
      const productsCount = await database.get(
        'SELECT COUNT(*) as count FROM products_cache WHERE store_id = ?',
        [storeId]
      );

      return {
        name: store.store_name,
        url: store.store_url,
        productsCount: productsCount?.count || 0
      };
    } catch (error) {
      logger.logError('فشل في جلب سياق المتجر', error);
      return { name: 'متجر سلة', url: '', productsCount: 0 };
    }
  }

  // إنشاء اقتراحات للعميل
  async generateSuggestions(storeId, message) {
    const commonSuggestions = [
      'عرض المنتجات الجديدة',
      'البحث عن منتج معين',
      'معلومات الشحن والتوصيل',
      'طرق الدفع المتاحة',
      'التواصل مع خدمة العملاء'
    ];

    // تخصيص الاقتراحات بناءً على الرسالة
    if (message.includes('سعر') || message.includes('تكلفة')) {
      return ['مقارنة الأسعار', 'العروض الحالية', 'خصومات متاحة'];
    }

    if (message.includes('شحن') || message.includes('توصيل')) {
      return ['مناطق التوصيل', 'أوقات التسليم', 'تكلفة الشحن'];
    }

    if (message.includes('دفع') || message.includes('شراء')) {
      return ['طرق الدفع', 'الدفع عند الاستلام', 'الدفع الإلكتروني'];
    }

    return commonSuggestions.slice(0, 3);
  }

  // البحث عن منتجات ذات صلة
  async findRelevantProducts(storeId, message) {
    try {
      // البحث في المنتجات المخزنة
      const products = await database.all(
        `SELECT product_data FROM products_cache 
         WHERE store_id = ? 
         ORDER BY last_updated DESC 
         LIMIT 5`,
        [storeId]
      );

      if (products.length === 0) {
        return [];
      }

      // تحليل الرسالة للبحث عن كلمات مفتاحية
      const keywords = this.extractKeywords(message);
      const relevantProducts = [];

      for (const product of products) {
        try {
          const productData = JSON.parse(product.product_data);
          const productText = `${productData.name} ${productData.description || ''}`.toLowerCase();
          
          // التحقق من وجود كلمات مفتاحية
          const hasKeyword = keywords.some(keyword => 
            productText.includes(keyword.toLowerCase())
          );

          if (hasKeyword) {
            relevantProducts.push({
              id: productData.id,
              name: productData.name,
              price: productData.price,
              image: productData.images?.[0]?.url || '',
              url: productData.url || ''
            });
          }
        } catch (parseError) {
          logger.logWarning('فشل في تحليل بيانات المنتج', { parseError });
        }
      }

      return relevantProducts.slice(0, 3);
    } catch (error) {
      logger.logError('فشل في البحث عن منتجات ذات صلة', error);
      return [];
    }
  }

  // استخراج الكلمات المفتاحية من الرسالة
  extractKeywords(message) {
    // إزالة كلمات الوقف العربية
    const stopWords = [
      'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك',
      'التي', 'الذي', 'التي', 'كان', 'كانت', 'يكون', 'تكون', 'أن', 'إن',
      'لا', 'ما', 'لم', 'لن', 'قد', 'كل', 'بعض', 'جميع', 'أي', 'كيف',
      'متى', 'أين', 'لماذا', 'ماذا', 'من', 'أين', 'كم', 'أي'
    ];

    const words = message
      .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.includes(word));

    return words;
  }

  // إنشاء محتوى SEO
  async generateSEOContent(storeId, type, data) {
    try {
      logger.logStart('إنشاء محتوى SEO', { storeId, type });

      const storeInfo = await this.getStoreContext(storeId);
      let prompt = '';

      switch (type) {
        case 'product_description':
          prompt = `اكتب وصف منتج محسن لمحركات البحث للمنتج التالي:
اسم المنتج: ${data.name}
الفئة: ${data.category || ''}
السعر: ${data.price || ''}

يجب أن يكون الوصف:
- جذاب ومقنع للعملاء
- محسن لمحركات البحث
- يحتوي على كلمات مفتاحية مناسبة
- باللغة العربية
- بين 150-300 كلمة`;
          break;

        case 'meta_description':
          prompt = `اكتب وصف ميتا محسن لمحركات البحث للصفحة التالية:
عنوان الصفحة: ${data.title}
نوع الصفحة: ${data.pageType || ''}
المحتوى الرئيسي: ${data.content || ''}

يجب أن يكون الوصف:
- بين 150-160 حرف
- جذاب ومحفز للنقر
- يحتوي على كلمات مفتاحية
- باللغة العربية`;
          break;

        case 'blog_post':
          prompt = `اكتب مقال مدونة محسن لمحركات البحث حول:
الموضوع: ${data.topic}
الكلمات المفتاحية: ${data.keywords?.join(', ') || ''}

يجب أن يكون المقال:
- بين 800-1200 كلمة
- مفيد وقيم للقراء
- محسن لمحركات البحث
- يحتوي على عناوين فرعية
- باللغة العربية`;
          break;

        default:
          throw new Error('نوع المحتوى غير مدعوم');
      }

      const content = await this.callOpenAI([
        { role: 'system', content: 'أنت خبير في كتابة المحتوى المحسن لمحركات البحث باللغة العربية.' },
        { role: 'user', content: prompt }
      ], 1000);

      logger.logSuccess('تم إنشاء محتوى SEO بنجاح');
      return content;

    } catch (error) {
      logger.logError('فشل في إنشاء محتوى SEO', error);
      throw error;
    }
  }

  // تحليل المشاعر للرسائل
  async analyzeSentiment(message) {
    try {
      const prompt = `حلل المشاعر في الرسالة التالية وأعط النتيجة كـ JSON:
الرسالة: "${message}"

أعط النتيجة بالتنسيق التالي:
{
  "sentiment": "positive|negative|neutral",
  "confidence": 0.8,
  "emotions": ["happy", "satisfied", "frustrated"],
  "urgency": "low|medium|high"
}`;

      const response = await this.callOpenAI([
        { role: 'system', content: 'أنت محلل مشاعر خبير للنصوص العربية.' },
        { role: 'user', content: prompt }
      ], 200);

      return JSON.parse(response);
    } catch (error) {
      logger.logError('فشل في تحليل المشاعر', error);
      return {
        sentiment: 'neutral',
        confidence: 0.5,
        emotions: [],
        urgency: 'low'
      };
    }
  }
}

module.exports = new AIService();
