{"name": "salla-ai-chatbot-integration", "version": "1.0.0", "description": "نظام تكامل الذكاء الاصطناعي مع متاجر سلة", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "test": "jest", "install-deps": "npm install", "setup": "node scripts/setup.js", "build": "echo 'Building project...' && npm install", "postinstall": "echo 'Installation complete! Run npm run setup to configure the system.'", "lint": "echo 'Linting code...'", "clean": "rimraf node_modules package-lock.json && npm install"}, "keywords": ["salla", "ai", "chatbot", "ecommerce", "arabic", "integration"], "author": "Augment Agent", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "socket.io": "^4.8.1", "sqlite3": "^5.1.6", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}