# إعدادات الخادم
PORT=3000
NODE_ENV=development

# إعدادات قاعدة البيانات
DB_PATH=./database/salla_ai.db

# إعدادات Salla API
SALLA_CLIENT_ID=4fd4acba-aaca-4105-975b-63932cdbd8d5
SALLA_CLIENT_SECRET=f1bbc3e7332836546c2f61f4f31b5de1
SALLA_BASE_URL=https://api.salla.dev/admin/v2
SALLA_OAUTH_URL=https://accounts.salla.sa/oauth2/authorize
SALLA_TOKEN_URL=https://accounts.salla.sa/oauth2/token

# إعدادات OpenAI (يجب الحصول عليها من openai.com)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# إعدادات الأمان
JWT_SECRET=your_super_secret_jwt_key_here
ENCRYPTION_KEY=your_encryption_key_here

# إعدادات الويب هوك
WEBHOOK_SECRET=your_webhook_secret_here
WEBHOOK_URL=https://yourdomain.com/webhooks/salla

# إعدادات البريد الإلكتروني (اختياري)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password

# إعدادات التخزين
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# إعدادات التسجيل
LOG_LEVEL=info
LOG_FILE=./logs/app.log
